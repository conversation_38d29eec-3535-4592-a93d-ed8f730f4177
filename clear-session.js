// Emergency session cleanup script
// Run this in the browser console if the app is hanging or slow

console.log('🚨 Starting emergency session cleanup...');

// Clear localStorage
const localStorageKeys = [
  'sb-sumruaeyfidjlssrmfrm-auth-token',
  'sb-sumruaeyfidjlssrmfrm-auth-token-code-verifier',
  'pollgpt_polls_cache',
  'pollgpt_retry_after_refresh',
  'supabase.auth.token'
];

localStorageKeys.forEach(key => {
  try {
    localStorage.removeItem(key);
    console.log(`✅ Removed localStorage: ${key}`);
  } catch (e) {
    console.warn(`❌ Failed to remove localStorage: ${key}`, e);
  }
});

// Clear sessionStorage
localStorageKeys.forEach(key => {
  try {
    sessionStorage.removeItem(key);
    console.log(`✅ Removed sessionStorage: ${key}`);
  } catch (e) {
    console.warn(`❌ Failed to remove sessionStorage: ${key}`, e);
  }
});

// Clear cookies
document.cookie.split(";").forEach(function(c) { 
  const cookieName = c.replace(/^ +/, "").replace(/=.*/, "");
  document.cookie = cookieName + "=;expires=" + new Date().toUTCString() + ";path=/";
  console.log(`✅ Cleared cookie: ${cookieName}`);
});

console.log('🎉 Emergency cleanup completed!');
console.log('🔄 Reloading page...');

// Reload the page
setTimeout(() => {
  window.location.reload();
}, 1000);
