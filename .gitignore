# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/cache
.yarn/plugnplay
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Testing
/coverage
/.nyc_output

# Next.js build outputs
/.next/
/out/

# Production build outputs (if applicable, e.g. for other parts of a monorepo or custom builds)
# /build
# /dist

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Compiled TypeScript files
/scripts/compiled/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local environment variables
# Never commit .env files containing secrets!
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local # Catch-all for other .local files

# Vercel
.vercel

# IDE and editor specific
.idea/
.vscode/
*.swp
*.swo
*~

# Debug and test files
/public/debug-*.html
/public/test-*.js
test-*.js
test-*.mjs
debug-*.js
debug-*.mjs
*-backup.ts
*-backup.js
/src/app/api/debug/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/
.directory

# Supabase
.supabase/

# Miscellaneous
.cache/
.temp/
.tmp/
logs
*.log

# Browser Extensions
/pollgpt-extension/
/simple-pollgpt-extension/
pollgpt-extension.zip

# Debug files and screenshots
debug-*.png
*-debug.png
debug-*.mjs
*-debug.mjs
screenshots/
screenshots-*/

# Test scripts
test-*.mjs
quick-*.mjs
simple-*.mjs
comprehensive-*.mjs
diagnose-*.mjs
apply-*.mjs

# Temporary SQL files (except important migrations)
*-rls-*.sql
set-*.sql
fix-dev-*.sql

# Documentation files for personal reference
*_ANALYSIS.md
*_VALIDATION.md
*_FIX.md

