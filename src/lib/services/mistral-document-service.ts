'use server';

import { Mistral } from '@mistralai/mistralai';

// Initialize the Mistral client with API key from environment variables
const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY || process.env.NEXT_PUBLIC_MISTRAL_API_KEY || '';

/**
 * Enhanced document extraction service using Mistral AI's document capabilities
 * This service uses the dedicated OCR API for PDFs and images, and the chat completion API
 * as a fallback for other document types.
 *
 * @see https://docs.mistral.ai/capabilities/document/
 */

// Validate API key at module level for better error handling
if (!MISTRAL_API_KEY) {
  console.error('MISTRAL_API_KEY is not configured in environment variables!');
}

/**
 * Extract content from a document using Mistral AI's document capabilities
 * @param fileBuffer Binary data of the document
 * @param mimeType MIME type of the document
 * @param fileName Original filename (for better context)
 * @returns Extracted and formatted text content
 */
export async function extractDocumentContent(
  fileBuffer: A<PERSON>y<PERSON>uffer,
  mimeType: string,
  fileName: string
): Promise<string> {
  try {
    console.log(`Processing document: ${fileName}, type: ${mimeType}, size: ${fileBuffer.byteLength} bytes`);

    if (!MISTRAL_API_KEY) {
      throw new Error('MISTRAL_API_KEY is not set in environment variables - Document extraction cannot proceed');
    }

    // Basic validation of file size
    if (fileBuffer.byteLength > 25 * 1024 * 1024) { // 25MB limit
      throw new Error('Document is too large (max 25MB). Please try a smaller file.');
    }

    // Initialize Mistral client
    const mistral = new Mistral({
      apiKey: MISTRAL_API_KEY
    });

    // Convert buffer to base64 for API
    const buffer = Buffer.from(fileBuffer);
    const base64Data = buffer.toString('base64');

    // Determine document type and customize prompt accordingly
    let extractionPrompt = "Please extract all the text content from this document. Format the output in a clean, structured way that preserves the original formatting as much as possible. Include all important information such as headings, paragraphs, bullet points, tables, and any other relevant content.";

    if (mimeType.includes('pdf')) {
      extractionPrompt += " For PDFs, preserve the document structure including sections, headers, and maintain table layouts.";
    } else if (mimeType.includes('image')) {
      extractionPrompt += " For this image, identify and extract all visible text, maintaining the reading order and structure.";
    }

    // Use Mistral's document understanding capabilities
    let response;
    try {
      // Check if the document is a PDF - use dedicated OCR API for PDFs
      if (mimeType.includes('pdf')) {
        console.log('Using Mistral OCR API for PDF document extraction');        // Use the OCR process endpoint for PDFs
        const ocrResponse = await mistral.ocr.process({
          model: "mistral-ocr-latest",
          document: {
            type: "document_url",
            documentUrl: `data:application/pdf;base64,${base64Data}`
          }
        });

        // Return the OCR results - combine markdown from all pages
        if (ocrResponse?.pages && ocrResponse.pages.length > 0) {
          const combinedText = ocrResponse.pages
            .map(page => page.markdown)
            .join('\n\n');
          return formatExtractedContent(combinedText, fileName);
        } else {
          throw new Error('OCR API returned empty results');
        }
      } else {
        // For images and other documents, use chat completion API
        console.log(`Using chat completion API for ${mimeType} document`);

        response = await mistral.chat.complete({
          model: "mistral-large-latest",
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: extractionPrompt
                },
                {
                  type: "image_url",
                  imageUrl: {
                    url: `data:${mimeType};base64,${base64Data}`
                  }
                }
              ]
            }
          ],
          maxTokens: 4000,
        });
      }
    } catch (apiError: unknown) {
      // Special handling for vision capability errors
      const errorMessage = apiError instanceof Error ? apiError.message : String(apiError);
      if (errorMessage.includes('vision capability') ||
          errorMessage.includes('vision') ||
          errorMessage.includes('multimodal')) {
        throw new Error(
          `Your Mistral API plan doesn't support document/image extraction. ` +
          `This feature requires a plan with vision capabilities. ` +
          `Error details: ${errorMessage}`
        );
      }
      throw apiError;
    }

  // Process and format the response
    let result = '';

    // Handle different response formats
    if (response.choices && response.choices.length > 0) {
      const firstChoice = response.choices[0];
      if (firstChoice?.message?.content) {
        if (typeof firstChoice.message.content === 'string') {
          result = firstChoice.message.content;
        } else if (Array.isArray(firstChoice.message.content)) {
          // Some API versions might return content as an array
          result = firstChoice.message.content
            .map(item => typeof item === 'string' ? item : item?.text || '')
            .join('\n');
        }
      }
    }

    if (result && result.length > 0) {
      console.log(`Successfully extracted content from document (${result.length} chars)`);

      // Format the extracted content for better readability
      return formatExtractedContent(result, fileName);
    }

    return 'No content could be extracted from the document. Please try a different file format.';
  } catch (error) {
    console.error('Document extraction error:', error);

    // Check if this is a PDF - we can use the fallback extractor
    if (mimeType.includes('pdf')) {
      console.log('Attempting to use fallback PDF text extractor');
      try {
        // We'll implement and call the fallback method directly here rather than throwing
        const { extractTextFromPdf } = await import('./pdf-text-extractor');
        const pdfText = await extractTextFromPdf(fileBuffer);

        if (pdfText && pdfText.length > 50) {
          console.log(`Successfully extracted ${pdfText.length} characters using fallback PDF extractor`);
          return formatExtractedContent(`Extracted from: ${fileName} (basic extraction)\n\n${pdfText}`, fileName);
        }
      } catch (fallbackError) {
        console.error('PDF fallback extraction also failed:', fallbackError);
        // Continue to the error throw below
      }
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Document extraction failed: ${errorMessage}`);
  }
}

/**
 * Format the extracted content for better readability
 * @param content Raw extracted content
 * @param fileName Original filename
 * @returns Formatted content
 */
function formatExtractedContent(content: string, fileName: string): string {
  // Remove any markdown code blocks that might be in the response
  let formattedContent = content.replace(/```[a-z]*\n|```/g, '');

  // Clean up excessive whitespace while preserving paragraph breaks
  formattedContent = formattedContent
    .replace(/\n{3,}/g, '\n\n')  // Replace 3+ newlines with 2
    .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with a single space
    .trim();

  // Add a header with the document name
  return `Extracted from: ${fileName}\n\n${formattedContent}`;
}
