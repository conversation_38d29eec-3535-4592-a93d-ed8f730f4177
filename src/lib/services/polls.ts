import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

// Types
export type QuestionType = 'single' | 'multiple' | 'likert' | 'open';

export interface QuestionOption {
  id: string;
  text: string;
  value: string;
}

export interface PollQuestion {
  id: string;
  text: string;
  type: QuestionType;
  options?: QuestionOption[];
  required: boolean;
  order: number;
}

export interface PollResponse {
  id: string;
  pollId: string;
  responses: Record<string, string | string[]>;
  submittedAt: string;
  respondentInfo?: {
    deviceType: string;
    browser: string;
    os: string;
    region?: string;
  }
  deviceType: string;
  browser: string;
  os: string;
  region?: string;
}

// Shape of a question object as it comes from the 'questions' table/join
export interface DbQuestionShape {
  id: string;
  poll_id: string;
  question_text: string;
  question_type: string; // Raw string type from DB, will be cast to QuestionType
  options?: QuestionOption[]; // Assuming options might be stored as JSON or similar
  required?: boolean;
  order?: number;
}

export interface Poll {
  id: string;
  title: string;
  description: string;
  questions: PollQuestion[]; // Actual question objects
  questions_count: number; // Number of questions
  createdAt: string;
  updatedAt: string;
  expiresAt: string | null;
  userId: string;
  status: 'draft' | 'active' | 'completed';
  responses_count: number; // Renamed from responses
  views_count: number;     // Renamed from views
  is_public: boolean;
  access_code?: string;
}

// Helper function to generate a unique ID
export const generateId = () => {
  return uuidv4();
};

// Interface for pagination results

// Shape of the payload for updating a poll in the database
export interface PollDbUpdatePayload {
  title?: string;
  description?: string;
  expires_at?: string | null;
  status?: 'draft' | 'active' | 'completed';
  is_public?: boolean;
  access_code?: string | null;
  // user_id should not be updatable here
  // created_at is fixed
  // responses and views are derived counts, not directly updated
}

export interface PaginatedPolls {
  polls: Poll[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

// Simple in-memory cache for polls data with longer expiration for better UX
const pollsCache = new Map<string, { data: PaginatedPolls, timestamp: number }>();
const CACHE_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes - longer cache for better UX
const STALE_WHILE_REVALIDATE_MS = 10 * 60 * 1000; // 10 minutes - serve stale data while fetching fresh

// Track last call time for dynamic timeout
let lastPollsCallTime = 0;

// Get polls for the current user with pagination support
export const getPolls = async (
  page: number = 1,
  pageSize: number = 10,
  fetchAll: boolean = false
): Promise<PaginatedPolls> => {
  try {
    // First check if Supabase connection is available
    const connectionStart = Date.now();

    // First try to get the current user with a shorter timeout (5s)
    const { data: { user }, error: userError } = await Promise.race([
      supabase.auth.getUser(),
      new Promise<{data:{user:null}, error: Error}>((_, reject) =>
        setTimeout(() => reject(new Error("Auth connection timed out")), 5000)
      )
    ]);

    const connectionTime = Date.now() - connectionStart;
    console.log(`Auth connection response time: ${connectionTime}ms`);

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`User authentication error: ${userError.message}`);
    }

    if (!user) {
      console.warn("No authenticated user found when fetching polls");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    console.log(`Fetching polls for user: ${user.id}, page: ${page}, pageSize: ${pageSize}, fetchAll: ${fetchAll}`);

    // Get polls from Supabase where user_id matches current user with timeout
    // Make sure we have auth context properly set for RLS policies to work
    const fetchStart = Date.now();

    // OPTIMIZATION: Fetch polls with counts in a single query using Postgres functions with pagination
    // Check in-memory cache first
    const cacheKey = `polls_${user.id}_${page}_${pageSize}_${fetchAll}`;
    const cachedData = pollsCache.get(cacheKey);

    // Check cache with stale-while-revalidate strategy
    if (cachedData && !fetchAll) {
      const now = Date.now();
      const cacheAge = now - cachedData.timestamp;

      if (cacheAge < STALE_WHILE_REVALIDATE_MS) {
        // Data is fresh, return immediately
        console.log(`Using fresh cached polls data for ${cacheKey}`);
        return cachedData.data;
      } else if (cacheAge < CACHE_EXPIRY_MS) {
        // Data is stale but not expired, return stale data and fetch fresh data in background
        console.log(`Using stale cached polls data for ${cacheKey}, fetching fresh data in background`);

        // Return stale data immediately
        const staleData = cachedData.data;

        // Fetch fresh data in background (don't await)
        setTimeout(async () => {
          try {
            console.log(`Background refresh for ${cacheKey}`);
            await getPolls(page, pageSize, true); // Force refresh in background
          } catch (error) {
            console.warn('Background refresh failed:', error);
          }
        }, 100);

        return staleData;
      } else {
        // Data is expired, remove from cache
        console.log(`Cached data for ${cacheKey} has expired`);
        pollsCache.delete(cacheKey);
      }
    }

    // Dynamic timeout based on session freshness
    const getTimeoutDuration = () => {
      // Check if this looks like a session that might need refresh
      const now = Date.now();
      const timeSinceLastCall = now - lastPollsCallTime;

      // If it's been more than 5 minutes since last call, use longer timeout for session refresh
      if (timeSinceLastCall > 5 * 60 * 1000) {
        console.log('Using extended timeout for potential session refresh');
        return 12000; // 12 seconds for session refresh scenarios (increased from 8s)
      }
      return 8000; // 8 seconds for normal calls (increased from 3s)
    };

    const timeoutMs = getTimeoutDuration();
    lastPollsCallTime = Date.now();

    const { data, error } = await Promise.race([
      supabase
        .rpc('get_polls_with_counts', {
          user_id_param: user.id,
          page_number: page,
          page_size: pageSize,
          fetch_all: fetchAll
        }),
      new Promise<{data:null, error: Error}>((_, reject) =>
        setTimeout(() => reject(new Error(`Database query timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);

    const fetchTime = Date.now() - fetchStart;
    console.log(`Poll fetch response time: ${fetchTime}ms`);

    if (error) {
      console.error("Error fetching polls:", error);
      // Detailed diagnostic information for debugging with error code if available
      if (error.message.includes('RLS') || error.message.includes('permission denied')) {
        console.error("RLS policy might be blocking access. Check database policies.", {
          userId: user.id,
          errorCode: error.code,
          query: 'get_polls_with_counts'
        });
        throw new Error(`Database access denied. Your session might have expired. Please try logging out and back in.`);
      } else if (error.message.includes('timed out') || fetchTime > timeoutMs * 0.9) {
        console.error("Database query timeout or approaching timeout", {
          fetchTime,
          timeoutMs,
          errorMessage: error.message
        });
        throw new Error(`Database query is taking too long. This could be due to high server load or missing indexes.`);
      } else if (error.message.includes('function get_polls_with_counts') && error.message.includes('does not exist')) {
        // Fallback to the original implementation if the function doesn't exist
        console.warn("Custom function not found, falling back to standard queries");
        const fallbackPolls = await getPolls_fallback(user.id);
        return {
          polls: fallbackPolls,
          totalCount: fallbackPolls.length,
          currentPage: 1,
          totalPages: 1,
          pageSize: fallbackPolls.length
        };
      } else {
        throw new Error(`Database error: ${error.message}`);
      }
    }

    // Handle null/undefined data case
    if (!data || data.length === 0) {
      console.log("No poll data returned");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    // Extract the total count from the first row (all rows have the same total_count value)
    const totalCount = data[0].total_count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    console.log(`Found ${data.length} polls (page ${page}/${totalPages}, total: ${totalCount})`);

    // Create result object to be returned (and cached)
    const result: PaginatedPolls = {
      polls: [],
      totalCount,
      currentPage: page,
      totalPages,
      pageSize
    };

    // Map the database format to our application format with improved data handling
    const polls: Poll[] = data.map(dbPoll => {
      // Define the question type from the database
      interface DbQuestion {
        id: string;
        poll_id: string;
        question_text: string;
        question_type: string;
        order?: number;
      }

      // Parse questions from the JSON array if available
      const questions = dbPoll.questions ? dbPoll.questions.map((q: DbQuestion) => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type as QuestionType,
        options: [], // We don't need options for the polls list view
        required: false, // Default value
        order: q.order || 0
      })) : [];

      return {
        id: dbPoll.id,
        title: dbPoll.title || 'Untitled Poll',
        description: dbPoll.description || '',
        questions: questions,
        questions_count: questions.length, // Calculate from parsed questions array
        createdAt: dbPoll.created_at,
        updatedAt: dbPoll.updated_at || dbPoll.created_at,
        expiresAt: null,
        userId: dbPoll.user_id,
        // Use status column directly (is_published no longer exists)
        status: dbPoll.status as 'draft'|'active'|'completed',
        responses_count: dbPoll.response_count || 0, // Use response_count from database function
        views_count: dbPoll.view_count || 0, // Use view_count from database function
        is_public: dbPoll.is_public !== undefined ? dbPoll.is_public : true,
        access_code: dbPoll.access_code
      };
    });

    // Update the result object with the processed polls
    result.polls = polls;

    // Cache the fetched data with timestamp for expiry check
    pollsCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    return result;
  } catch (error) {
    console.error("Error fetching polls:", error);
    // Always return a valid PaginatedPolls object with empty polls array
    return {
      polls: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      pageSize
    };
  }
};

// Fallback implementation using multiple queries
// This is used if the optimized RPC function doesn't exist

// Delete a poll
export const deletePoll = async (id: string): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      // Using console.error for service layer, UI layer can use toast
      console.error("User must be logged in to delete polls.");
      return false;
    }

    // First, delete related records (questions, responses, answers)
    // This order is important due to foreign key constraints

    // Delete answers associated with responses to this poll
    const { error: deleteAnswersError } = await supabase
      .from('answers')
      .delete()
      .in('response_id', supabase.from('responses').select('id').eq('poll_id', id));

    if (deleteAnswersError) {
      console.error('Error deleting answers:', deleteAnswersError);
      throw new Error(`Failed to delete poll's answers: ${deleteAnswersError.message}`);
    }

    // Delete responses to this poll
    const { error: deleteResponsesError } = await supabase
      .from('responses')
      .delete()
      .eq('poll_id', id);

    if (deleteResponsesError) {
      console.error('Error deleting responses:', deleteResponsesError);
      throw new Error(`Failed to delete poll's responses: ${deleteResponsesError.message}`);
    }

    // Delete questions associated with this poll
    const { error: deleteQuestionsError } = await supabase
      .from('questions')
      .delete()
      .eq('poll_id', id);

    if (deleteQuestionsError) {
      console.error('Error deleting questions:', deleteQuestionsError);
      throw new Error(`Failed to delete poll's questions: ${deleteQuestionsError.message}`);
    }

    // Finally, delete the poll itself
    const { error: deletePollError } = await supabase
      .from('polls')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id); // Ensure user can only delete their own polls

    if (deletePollError) {
      console.error("Error deleting poll in service:", deletePollError.message);
      throw new Error(`Failed to delete poll: ${deletePollError.message}`); // Re-throw as a new error for consistent error handling
    }

    console.log("Poll deleted successfully from service!");
    return true;
  } catch (error: unknown) {
    let errorMessage = "An unknown error occurred while deleting the poll.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error("Error deleting poll in service:", errorMessage);
    throw new Error(`Failed to delete poll: ${errorMessage}`);
  }
};

export const duplicatePoll = async (originalPollId: string): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to duplicate polls.");
      return null;
    }

    const originalPoll = await getPollById(originalPollId);
    if (!originalPoll) {
      console.error(`Original poll with ID ${originalPollId} not found for duplication.`);
      return null;
    }

    // Ensure the user owns the original poll (optional, getPollById might already handle this if it filters by user)
    if (originalPoll.userId !== user.id) {
        console.error(`User ${user.id} does not own original poll ${originalPollId}. Cannot duplicate.`);
        return null;
    }

    const newPollId = generateId();
    const now = new Date().toISOString();

    const newPollData: Partial<Poll> = {
      // id: newPollId, // createPoll will handle ID generation if not provided, or we can pass it.
      title: `Copy of ${originalPoll.title}`,
      description: originalPoll.description,
      userId: user.id, // Explicitly set new owner, though createPoll should do this
      createdAt: now, // createPoll will set this
      updatedAt: now, // createPoll will set this
      expiresAt: originalPoll.expiresAt,
      status: 'draft', // New duplicate starts as draft
      is_public: originalPoll.is_public,
      access_code: originalPoll.access_code,
      questions_count: originalPoll.questions_count !== undefined ? originalPoll.questions_count : originalPoll.questions.length,
      responses_count: 0,
      views_count: 0,
      questions: originalPoll.questions.map(q => ({
        ...q, // Spread original question properties
        id: generateId(), // New ID for the duplicated question
        poll_id: newPollId, // This will be overridden by createPoll if it handles questions internally
                               // or needs to be set if questions are created in a separate step.
        options: q.options ? q.options.map(opt => ({ ...opt, id: generateId() })) : [],
      }))
    };

    // Use createPoll to handle insertion of poll and its questions
    // createPoll needs to be robust enough to handle pre-defined question IDs or generate new ones if not provided.
    // For simplicity, we assume createPoll can take full Poll structure with questions.
    // If createPoll doesn't handle questions directly, we'd insert poll first, then questions.

    const createdPoll = await createPoll(newPollData);
    // createPoll internally calls getPollById, so this should be the complete new poll.

    if (!createdPoll) {
        console.error(`Failed to create a duplicate of poll ${originalPollId}.`);
        return null;
    }

    console.log(`Poll ${originalPollId} duplicated successfully as ${createdPoll.id}.`);
    return createdPoll;

  } catch (error) {
    console.error(`Error duplicating poll ${originalPollId}:`, error);
    throw error;
  }
};

export const closePoll = async (id: string): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to close polls.");
      return null;
    }

    const updates: Partial<PollDbUpdatePayload> = {
      status: 'completed',
      // expires_at: new Date().toISOString(), // Optionally set expires_at to now when closing
    };

    // The updatePoll function already handles setting updatedAt and user_id check
    const updatedPoll = await updatePoll(id, updates as Partial<Poll>); // Cast needed if updates doesn't perfectly match Partial<Poll>

    if (!updatedPoll) {
      console.error(`Failed to close poll ${id}. It might not exist or user may not have permission.`);
      return null;
    }

    console.log(`Poll ${id} closed successfully.`);
    return updatedPoll;

  } catch (error) {
    console.error(`Error closing poll ${id}:`, error);
    throw error;
  }
};

// Create a new poll
export const createPoll = async (pollData: Partial<Poll>): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to create polls.");
      return null;
    }

    // Prepare data for insertion, ensuring all required fields for DB are present
    const newPollData = {
      title: pollData.title || 'Untitled Poll',
      description: pollData.description || '',
      // questions: pollData.questions || [], // Questions are typically handled as related records
      expires_at: pollData.expiresAt || null,
      user_id: user.id,
      status: pollData.status || 'draft',
      is_public: pollData.is_public !== undefined ? pollData.is_public : true,
      access_code: pollData.access_code || null,
    };

    const { data: insertedPoll, error: insertError } = await supabase
      .from('polls')
      .insert(newPollData)
      .select()
      .single();

    if (insertError) {
      console.error('Error creating poll:', insertError);
      throw insertError;
    }
    if (!insertedPoll) return null;

    // Handle questions if provided
    if (pollData.questions && pollData.questions.length > 0) {
      const questionsToInsert = pollData.questions.map(q => ({
        // Ensure all fields required by 'questions' table are mapped
        poll_id: insertedPoll.id,
        question_text: q.text,
        question_type: q.type,
        options: q.options || [],
        required: q.required !== undefined ? q.required : false,
        order: q.order !== undefined ? q.order : 0,
        // id: q.id // Let DB generate question IDs or handle if provided
      }));
      const { error: questionError } = await supabase.from('questions').insert(questionsToInsert);
      if (questionError) {
        console.error('Error inserting questions for new poll:', questionError);
        // Optionally delete the created poll or mark as incomplete
      }
    }

    // Re-fetch the poll with questions to ensure consistency or map manually
    // It's often better to return the inserted data directly if possible,
    // or construct the Poll object from insertedPoll and questionsToInsert if successful.
    // For simplicity here, we re-fetch.
    return getPollById(insertedPoll.id);

  } catch (error) {
    console.error('Service error creating poll:', error);
    throw error;
  }
};

// Get a single poll by its ID
export const getPollById = async (id: string): Promise<Poll | null> => {
  try {
    // Base query
    const query = supabase
      .from('polls')
      .select(`
        *,
        questions (id, poll_id, question_text, question_type, options, required, "order")
      `)
      .eq('id', id);

    // If not in preview mode AND not for authenticated users viewing their own polls, check that the poll is published
    // For now, we'll remove this constraint to allow viewing draft polls
    // Draft polls will be handled by RLS policies at the database level

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') { // Resource not found
        console.log(`Poll with id ${id} not found.`);
        return null;
      }
      console.error(`Error fetching poll by id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    // Map DB response to Poll type
    const poll: Poll = {
      id: data.id,
      title: data.title,
      description: data.description,
      questions: data.questions ? data.questions.map((q: DbQuestionShape) => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type as QuestionType,
        options: q.options || [],
        required: q.required !== undefined ? q.required : false,
        order: q.order !== undefined ? q.order : 0
      })) : [],
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      expiresAt: data.expires_at,
      userId: data.user_id,
      status: data.status as 'draft' | 'active' | 'completed',
      questions_count: data.questions ? data.questions.length : 0,
      responses_count: 0, // getPollById doesn't fetch response_count directly, set to 0
      views_count: 0,     // getPollById doesn't fetch view_count directly, set to 0
      is_public: data.is_public,
      access_code: data.access_code,
    };
    return poll;

  } catch (error) {
    console.error(`Service error fetching poll by id ${id}:`, error);
    throw error;
  }
};

// Update an existing poll
export const updatePoll = async (id: string, updates: Partial<Poll>): Promise<Poll | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to update polls.");
      return null;
    }

    const { questions, ...pollUpdates } = updates;
    // Map Poll fields to DB column names if different (e.g., expiresAt to expires_at)
    const dbUpdates: PollDbUpdatePayload = {};

    // Explicitly map fields to ensure type safety and correct column names
    if (pollUpdates.title !== undefined) dbUpdates.title = pollUpdates.title;
    if (pollUpdates.description !== undefined) dbUpdates.description = pollUpdates.description;
    if (pollUpdates.status !== undefined) dbUpdates.status = pollUpdates.status;
    if (pollUpdates.is_public !== undefined) dbUpdates.is_public = pollUpdates.is_public;
    if (pollUpdates.access_code !== undefined) dbUpdates.access_code = pollUpdates.access_code;
    if (pollUpdates.expiresAt !== undefined) dbUpdates.expires_at = pollUpdates.expiresAt;
    // Note: createdAt, userId, responses, and views are not part of PollDbUpdatePayload
    // and thus are not (and should not be) included in dbUpdates.

    const { data: updatedPollData, error: updateError } = await supabase
      .from('polls')
      .update(dbUpdates)
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user owns the poll
      .select()
      .single();

    if (updateError) {
      console.error(`Error updating poll ${id}:`, updateError);
      throw updateError;
    }
    if (!updatedPollData) return null;

    // Handle questions update if 'questions' array is provided in updates
    if (questions) {
      // Delete existing questions for this poll
      const { error: deleteError } = await supabase.from('questions').delete().eq('poll_id', id);
      if (deleteError) {
        console.error(`Error deleting old questions for poll ${id}:`, deleteError);
        // Potentially throw error or handle more gracefully
      }

      // Insert new questions
      if (questions.length > 0) {
        const newQuestionsData = questions.map(q => ({
          poll_id: id,
          question_text: q.text,
          question_type: q.type,
          options: q.options || [],
          required: q.required !== undefined ? q.required : false,
          order: q.order !== undefined ? q.order : 0,
          // id: q.id // Let DB generate question IDs or handle if provided
        }));
        const { error: insertError } = await supabase.from('questions').insert(newQuestionsData);
        if (insertError) {
          console.error(`Error inserting new questions for poll ${id}:`, insertError);
          // Potentially throw error or handle more gracefully
        }
      }
    }

    // Re-fetch the poll with updated questions for consistency
    return getPollById(id);

  } catch (error) {
    console.error(`Service error updating poll ${id}:`, error);
    throw error;
  }
};

// Initialize with sample data (stub)
export const initializeWithSampleData = async (): Promise<void> => {
  console.warn("initializeWithSampleData() is a stub and not fully implemented.");
  return Promise.resolve();
};

// Get responses for a specific poll
export const getPollResponses = async (pollId: string): Promise<PollResponse[] | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error("User must be logged in to get poll responses.");
      return null;
    }

    // First verify the user owns this poll
    const { data: pollData } = await supabase
      .from('polls')
      .select('user_id')
      .eq('id', pollId)
      .single();

    if (!pollData || pollData.user_id !== user.id) {
      console.error("User does not have permission to access this poll's responses.");
      return null;
    }

    // Get all responses for this poll with answers
    const { data: dbResponses, error } = await supabase
      .from('responses')
      .select(`
        id,
        created_at,
        respondent_info,
        answers (
          id,
          question_id,
          answer_value
        )
      `)
      .eq('poll_id', pollId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching poll responses:', error);
      return null;
    }

    if (!dbResponses) {
      return [];
    }

    // Transform the database format to PollResponse format
    const transformedResponses: PollResponse[] = dbResponses.map(dbResponse => {
      // Convert answers array to responses object
      const responses: Record<string, string | string[]> = {};

      if (dbResponse.answers) {
        dbResponse.answers.forEach(answer => {
          try {
            // Try to parse as JSON (for multi-select answers)
            const parsedValue = JSON.parse(answer.answer_value);
            responses[answer.question_id] = parsedValue;
          } catch {
            // If parsing fails, use as string (for single answers)
            responses[answer.question_id] = answer.answer_value;
          }
        });
      }

      // Extract respondent info from metadata if available
      const respondentInfo = dbResponse.respondent_info || {};

      return {
        id: dbResponse.id,
        pollId: pollId,
        responses: responses,
        submittedAt: dbResponse.created_at,
        respondentInfo: {
          deviceType: respondentInfo.deviceType || 'unknown',
          browser: respondentInfo.browser || 'unknown',
          os: respondentInfo.os || 'unknown',
          region: respondentInfo.region
        },
        // Legacy format support (these might be used by the UI)
        deviceType: respondentInfo.deviceType || 'unknown',
        browser: respondentInfo.browser || 'unknown',
        os: respondentInfo.os || 'unknown',
        region: respondentInfo.region
      };
    });

    console.log(`Transformed ${transformedResponses.length} responses for poll ${pollId}`);

    // Debug: log first response structure
    if (transformedResponses.length > 0) {
      console.log('Sample transformed response:', {
        id: transformedResponses[0].id,
        responseKeys: Object.keys(transformedResponses[0].responses),
        submittedAt: transformedResponses[0].submittedAt
      });
    }

    return transformedResponses;
  } catch (error) {
    console.error('Error in getPollResponses:', error);
    return null;
  }
};

// Increment the view count for a poll
export const incrementPollViews = async (pollId: string): Promise<boolean> => {
  try {
    const { error } = await supabase.rpc('increment_poll_views', { poll_id: pollId });

    if (error) {
      // If the RPC function doesn't exist, fall back to a direct update
      console.warn('RPC function not found, using direct update');
      const { error: updateError } = await supabase
        .from('polls')
        .update({ views: supabase.sql`views + 1` })
        .eq('id', pollId);

      if (updateError) {
        console.error('Error incrementing poll views:', updateError);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error incrementing poll views:', error);
    return false;
  }
};

// Check if a user has already responded to a poll
export const hasUserRespondedToPoll = async (pollId: string): Promise<boolean> => {
  try {
    console.log(`[DEBUG] hasUserRespondedToPoll called for pollId: ${pollId}`);
    const { data: { user } } = await supabase.auth.getUser();
    console.log(`[DEBUG] User status:`, user ? `Authenticated (${user.id})` : 'Anonymous');

    // For authenticated users, check the database
    if (user) {
      console.log(`[DEBUG] Checking database for authenticated user responses`);
      const { error, count } = await supabase
        .from('responses')
        .select('id', { count: 'exact' })
        .eq('poll_id', pollId)
        .eq('user_id', user.id);

      if (error) {
        console.error('[DEBUG] Error checking if user responded to poll:', error);
        return false;
      }

      const hasResponded = count !== null && count > 0;
      console.log(`[DEBUG] Database check result: count=${count}, hasResponded=${hasResponded}`);
      return hasResponded;
    }

    // For anonymous users, check localStorage (client-side only)
    if (typeof window !== 'undefined') {
      console.log(`[DEBUG] Checking localStorage for anonymous user`);
      const storageKey = `poll_response_${pollId}`;
      const storedResponse = localStorage.getItem(storageKey);
      console.log(`[DEBUG] localStorage check - key: ${storageKey}, value:`, storedResponse);
      const hasStoredResponse = storedResponse !== null;
      console.log(`[DEBUG] localStorage check result: hasStoredResponse=${hasStoredResponse}`);
      return hasStoredResponse;
    }

    // Server-side check for anonymous users
    console.log(`[DEBUG] Server-side check for anonymous user - returning false`);
    return false;
  } catch (error) {
    console.error('[DEBUG] Error in hasUserRespondedToPoll:', error);
    return false;
  }
};

// Add a response to a poll
interface PollResponsePayload {
  pollId: string;
  responses: Record<string, string | string[]>;
  respondentInfo?: {
    deviceType: string;
    browser?: string;
    os?: string;
    region?: string;
  };
}

export const addPollResponse = async (payload: PollResponsePayload): Promise<boolean> => {
  try {
    console.log(`[DEBUG] addPollResponse called for pollId: ${payload.pollId}`);
    const { pollId, responses, respondentInfo } = payload;
    const { data: { user } } = await supabase.auth.getUser();
    console.log(`[DEBUG] User status:`, user ? `Authenticated (${user.id})` : 'Anonymous');

    // Create the response record
    const responseId = uuidv4();
    console.log(`[DEBUG] Created response ID: ${responseId}`);

    const { error: responseError } = await supabase
      .from('responses')
      .insert({
        id: responseId,
        poll_id: pollId,
        user_id: user?.id || null, // Allow anonymous responses
        respondent_info: respondentInfo,
      });

    if (responseError) {
      console.error('[DEBUG] Error creating response:', responseError);
      return false;
    }
    console.log(`[DEBUG] Successfully created response in database`);

    // Process and insert all answers
    // Convert from Record<string, string | string[]> to array of formatted answers
    const formattedAnswers = Object.entries(responses).map(([questionId, value]) => {
      return {
        id: uuidv4(),
        response_id: responseId,
        question_id: questionId,
        answer_value: typeof value === 'string' ? value : JSON.stringify(value),
      };
    });

    console.log(`[DEBUG] Formatted ${formattedAnswers.length} answers for insertion`);

    const { error: answersError } = await supabase
      .from('answers')
      .insert(formattedAnswers);

    if (answersError) {
      console.error('[DEBUG] Error adding answers:', answersError);
      // Try to clean up the response if answers failed
      await supabase.from('responses').delete().eq('id', responseId);
      return false;
    }
    console.log(`[DEBUG] Successfully inserted all answers`);

    // For anonymous users, also store in localStorage to prevent duplicate responses
    if (!user && typeof window !== 'undefined') {
      console.log(`[DEBUG] Storing response in localStorage for anonymous user`);
      const storageKey = `poll_response_${pollId}`;
      const storageData = {
        responseId: responseId,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(storageKey, JSON.stringify(storageData));
      console.log(`[DEBUG] localStorage stored - key: ${storageKey}, data:`, storageData);

      // Verify it was stored
      const verification = localStorage.getItem(storageKey);
      console.log(`[DEBUG] localStorage verification:`, verification);
    } else if (user) {
      console.log(`[DEBUG] Authenticated user - not storing in localStorage`);
    } else {
      console.log(`[DEBUG] Server-side execution - cannot store in localStorage`);
    }

    return true;
  } catch (error) {
    console.error('[DEBUG] Error in addPollResponse:', error);
    return false;
  }
};

// Fallback implementation using multiple queries
// This is used if the optimized RPC function doesn't exist
export const getPolls_fallback = async (userId: string): Promise<Poll[]> => {
  try {
    // Get polls from Supabase where user_id matches current user
    const { data: pollsData, error: pollsError } = await supabase
      .from('polls')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (pollsError) {
      console.error("Error fetching polls:", pollsError);
      return [];
    }

    if (!pollsData || pollsData.length === 0) {
      return [];
    }

    // Get all questions for these polls
    const pollIds = pollsData.map(poll => poll.id);
    const { data: questionsData, error: questionsError } = await supabase
      .from('questions')
      .select('*')
      .in('poll_id', pollIds)
      .order('order', { ascending: true });

    if (questionsError) {
      console.error("Error fetching questions:", questionsError);
    }

    // Count responses for each poll
    const pollsWithCounts = await Promise.all(pollsData.map(async (poll) => {
      const { count: responseCount, error: countError } = await supabase
        .from('responses')
        .select('id', { count: 'exact', head: true })
        .eq('poll_id', poll.id);

      if (countError) {
        console.error(`Error counting responses for poll ${poll.id}:`, countError);
      }

      // Group questions by poll
      const pollQuestions = questionsData?.filter(q => q.poll_id === poll.id) || [];

      return {
        ...poll,
        questions: pollQuestions,
        questions_count: pollQuestions.length, // Calculate questions_count
        responses_count: responseCount || 0,    // Use responses_count
        views_count: 0 // We don't track views_count in the fallback implementation, so set to 0
      };
    }));

    // Map the database format to our application format
    const polls: Poll[] = pollsWithCounts.map(dbPoll => {
      // dbPoll here is the result from the map above, already containing questions_count, responses_count, views_count
      const questions = dbPoll.questions ? dbPoll.questions.map(q => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type as QuestionType,
        options: [], // We don't need options for the polls list view
        required: q.required || false,
        order: q.order || 0
      })) : [];

      return {
        id: dbPoll.id,
        title: dbPoll.title || 'Untitled Poll',
        description: dbPoll.description || '',
        questions: questions,
        questions_count: questions.length,
        createdAt: dbPoll.created_at,
        updatedAt: dbPoll.updated_at || dbPoll.created_at,
        expiresAt: null,
        userId: dbPoll.user_id,
        // Prefer explicit status column if available
      status: dbPoll.status as 'draft' | 'active' | 'completed',
        responses_count: dbPoll.responses_count || 0,
        views_count: dbPoll.views_count || 0,
        is_public: dbPoll.is_public !== undefined ? dbPoll.is_public : true,
        access_code: dbPoll.access_code
      };
    }); // Closes the callback for pollsWithCounts.map

    return polls;
  } catch (error) {
    console.error("Error in fallback poll fetching:", error);
    return [];
  }
};
