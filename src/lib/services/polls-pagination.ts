import { supabase } from '@/lib/supabase';
import { Poll } from './polls';

// Interface for pagination results
export interface PaginatedPolls {
  polls: Poll[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

// Get polls for the current user with pagination support
export const getPaginatedPolls = async (
  page: number = 1,
  pageSize: number = 10,
  fetchAll: boolean = false
): Promise<PaginatedPolls> => {
  try {
    // First check if Supabase connection is available
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`User authentication error: ${userError.message}`);
    }

    if (!user) {
      console.warn("No authenticated user found when fetching polls");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    console.log(`Fetching polls for user: ${user.id}, page: ${page}, pageSize: ${pageSize}, fetchAll: ${fetchAll}`);

    // Get polls from Supabase where user_id matches current user with timeout
    const { data, error } = await supabase
      .rpc('get_polls_with_counts', { 
        user_id_param: user.id,
        page_number: page,
        page_size: pageSize,
        fetch_all: fetchAll
      });

    if (error) {
      console.error("Error fetching polls:", error);
      throw new Error(`Database error: ${error.message}`);
    }

    // Handle null/undefined data case
    if (!data || data.length === 0) {
      console.log("No poll data returned");
      return { polls: [], totalCount: 0, currentPage: page, totalPages: 0, pageSize };
    }

    // Extract the total count from the first row (all rows have the same total_count value)
    const totalCount = data[0].total_count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);
    
    console.log(`Found ${data.length} polls (page ${page}/${totalPages}, total: ${totalCount})`);

    // Map the database format to our application format
    const polls = data.map(dbPoll => {
      // Parse questions from the JSON array if available
      const questions = dbPoll.questions ? dbPoll.questions.map(q => ({
        id: q.id,
        text: q.question_text,
        type: q.question_type,
        options: [], // We don't need options for the polls list view
        required: false, // Default value
        order: q.order || 0
      })) : [];

      return {
        id: dbPoll.id,
        title: dbPoll.title || 'Untitled Poll',
        description: dbPoll.description || '',
        questions: questions,
        createdAt: dbPoll.created_at,
        updatedAt: dbPoll.updated_at || dbPoll.created_at,
        expiresAt: null, // No expires_at column in the database
        userId: dbPoll.user_id,
        status: dbPoll.is_published ? 'active' : 'draft',
        responses: dbPoll.response_count || 0,
        views: dbPoll.view_count || 0,
        is_public: dbPoll.is_public !== undefined ? dbPoll.is_public : true,
        access_code: dbPoll.access_code
      };
    });

    // Return the paginated polls object
    return {
      polls,
      totalCount,
      currentPage: page,
      totalPages,
      pageSize
    };
  } catch (error) {
    console.error("Error fetching polls:", error);
    // Always return a valid PaginatedPolls object with empty polls array
    return {
      polls: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      pageSize
    };
  }
};
