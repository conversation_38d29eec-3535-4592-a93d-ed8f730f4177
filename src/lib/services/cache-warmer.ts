/**
 * Cache Warming Service
 *
 * Implements intelligent cache warming strategies to ensure optimal performance:
 * - Predictive warming based on usage patterns
 * - Schedule-based warming for known high-traffic periods
 * - Event-driven warming triggered by data changes
 * - Priority-based warming for critical data
 */

import { redisCacheService as redisCache } from './redis-cache';
import { DatabasePaginator } from './database-paginator';
import { JobQueue } from './job-queue';
// import { PerformanceMonitor } from './performance-monitor';
import { supabase } from '@/lib/supabase';

export interface WarmingStrategy {
  name: string;
  enabled: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  schedule?: string;          // Cron expression
  triggers?: string[];        // Event triggers
  conditions?: WarmingCondition[];
  targets: WarmingTarget[];
}

export interface WarmingCondition {
  type: 'time' | 'usage' | 'cache_miss' | 'data_change';
  threshold?: number;
  timeRange?: string;
  comparator?: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
}

export interface WarmingTarget {
  type: 'query' | 'page' | 'computation' | 'api_response';
  table?: string;
  query?: string;
  parameters?: Record<string, unknown>;
  cacheKey?: string;
  ttl?: number;
  estimatedCost?: number;     // Computation cost (1-10)
}

export interface WarmingJob {
  id: string;
  strategy: string;
  target: WarmingTarget;
  priority: number;
  scheduledAt: number;
  attempts: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: WarmingResult;
}

export interface WarmingResult {
  success: boolean;
  cacheKey: string;
  dataSize: number;
  executionTime: number;
  error?: string;
  hitCount?: number;
}

export interface WarmingStats {
  totalJobs: number;
  successfulJobs: number;
  failedJobs: number;
  averageExecutionTime: number;
  cacheHitImprovement: number;
  strategiesPerformance: Record<string, StrategyStats>;
}

export interface StrategyStats {
  executions: number;
  successRate: number;
  averageCost: number;
  cacheHitImprovement: number;
  lastExecution: number;
}

export class CacheWarmer {
  private strategies = new Map<string, WarmingStrategy>();
  private activeJobs = new Map<string, WarmingJob>();
  private stats: WarmingStats;
  private isRunning = false;

  // Predefined warming strategies
  private static readonly DEFAULT_STRATEGIES: WarmingStrategy[] = [
    {
      name: 'popular_polls',
      enabled: true,
      priority: 'high',
      schedule: '0 */6 * * *', // Every 6 hours
      conditions: [
        { type: 'cache_miss', threshold: 5, timeRange: '1h' }
      ],
      targets: [
        {
          type: 'query',
          table: 'polls',
          query: 'popular_recent',
          parameters: { limit: 50, timeframe: '7d' },
          ttl: 3600
        },
        {
          type: 'page',
          cacheKey: 'polls:trending',
          ttl: 1800
        }
      ]
    },
    {
      name: 'user_dashboards',
      enabled: true,
      priority: 'medium',
      schedule: '0 8,20 * * *', // 8 AM and 8 PM
      conditions: [
        { type: 'time', timeRange: 'peak_hours' }
      ],
      targets: [
        {
          type: 'query',
          table: 'profiles',
          query: 'active_users_dashboard_data',
          ttl: 900
        }
      ]
    },
    {
      name: 'simulation_cache',
      enabled: true,
      priority: 'critical',
      triggers: ['new_simulation_request'],
      conditions: [
        { type: 'usage', threshold: 10, timeRange: '5m' }
      ],
      targets: [
        {
          type: 'computation',
          cacheKey: 'simulations:common_demographics',
          ttl: 7200,
          estimatedCost: 8
        }
      ]
    },
    {
      name: 'api_responses',
      enabled: true,
      priority: 'medium',
      schedule: '*/30 * * * *', // Every 30 minutes
      targets: [
        {
          type: 'api_response',
          cacheKey: 'api:poll_stats',
          ttl: 1800
        },
        {
          type: 'api_response',
          cacheKey: 'api:trending_topics',
          ttl: 3600
        }
      ]
    },
    {
      name: 'predictive_content',
      enabled: true,
      priority: 'low',
      schedule: '0 2 * * *', // 2 AM daily
      conditions: [
        { type: 'data_change', threshold: 100 }
      ],
      targets: [
        {
          type: 'page',
          cacheKey: 'content:homepage',
          ttl: 14400
        },
        {
          type: 'query',
          table: 'polls',
          query: 'recent_with_stats',
          parameters: { limit: 100 },
          ttl: 3600
        }
      ]
    }
  ];

  constructor() {
    this.stats = {
      totalJobs: 0,
      successfulJobs: 0,
      failedJobs: 0,
      averageExecutionTime: 0,
      cacheHitImprovement: 0,
      strategiesPerformance: {}
    };

    this.initializeStrategies();
  }

  /**
   * Initialize warming strategies
   */
  private initializeStrategies(): void {
    CacheWarmer.DEFAULT_STRATEGIES.forEach(strategy => {
      this.strategies.set(strategy.name, strategy);
      this.stats.strategiesPerformance[strategy.name] = {
        executions: 0,
        successRate: 1.0,
        averageCost: 0,
        cacheHitImprovement: 0,
        lastExecution: 0
      };
    });
  }

  /**
   * Start the cache warming service
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Cache warmer is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting cache warming service...');

    // Schedule periodic warming jobs
    this.scheduleWarmingJobs();

    // Start worker to process warming jobs
    this.startWarmingWorker();

    // Monitor cache performance for adaptive warming
    this.startPerformanceMonitoring();

    console.log('Cache warming service started successfully');
  }

  /**
   * Stop the cache warming service
   */
  public async stop(): Promise<void> {
    this.isRunning = false;
    console.log('Cache warming service stopped');
  }

  /**
   * Add a custom warming strategy
   */
  public addStrategy(strategy: WarmingStrategy): void {
    this.strategies.set(strategy.name, strategy);
    this.stats.strategiesPerformance[strategy.name] = {
      executions: 0,
      successRate: 1.0,
      averageCost: 0,
      cacheHitImprovement: 0,
      lastExecution: 0
    };
  }

  /**
   * Trigger warming for a specific strategy
   */
  public async warmStrategy(strategyName: string): Promise<WarmingResult[]> {
    const strategy = this.strategies.get(strategyName);
    if (!strategy || !strategy.enabled) {
      throw new Error(`Strategy '${strategyName}' not found or disabled`);
    }

    const results: WarmingResult[] = [];

    for (const target of strategy.targets) {
      try {
        const result = await this.executeWarmingTarget(target);
        results.push(result);

        if (result.success) {
          this.stats.successfulJobs++;
        } else {
          this.stats.failedJobs++;
        }
      } catch (error) {
        console.error(`Warming target failed:`, error);
        results.push({
          success: false,
          cacheKey: target.cacheKey || 'unknown',
          dataSize: 0,
          executionTime: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        this.stats.failedJobs++;
      }
    }

    this.stats.totalJobs += results.length;
    this.updateStrategyStats(strategyName, results);

    return results;
  }

  /**
   * Trigger warming based on an event
   */
  public async warmOnEvent(eventName: string): Promise<void> {
    const triggeredStrategies = Array.from(this.strategies.values())
      .filter(strategy =>
        strategy.enabled &&
        strategy.triggers?.includes(eventName)
      );

    for (const strategy of triggeredStrategies) {
      // Check conditions
      const shouldWarm = await this.evaluateConditions(strategy.conditions || []);

      if (shouldWarm) {
        // Queue warming job
        const job: WarmingJob = {
          id: `${strategy.name}_${Date.now()}`,
          strategy: strategy.name,
          target: strategy.targets[0], // Use first target for event-driven warming
          priority: this.getPriorityScore(strategy.priority),
          scheduledAt: Date.now(),
          attempts: 0,
          status: 'pending'
        };

        // Use a different approach to add the job since getInstance() is not available
        const jobQueue = new JobQueue();
        await jobQueue.addJob('cache_warmup', {
          jobId: job.id,
          strategyName: strategy.name,
          target: job.target
        });
      }
    }
  }

  /**
   * Get warming statistics
   */
  public getStats(): WarmingStats {
    return { ...this.stats };
  }

  /**
   * Get cache warming recommendations based on performance data
   */
  public async getRecommendations(): Promise<{
    suggestedStrategies: string[];
    optimizationOpportunities: string[];
    performanceIssues: string[];
  }> {
    const recommendations = {
      suggestedStrategies: [],
      optimizationOpportunities: [],
      performanceIssues: []
    };

    // Analyze cache miss patterns
    const cacheStats = await redisCache.getStats();
    // Check if hitRate exists in cacheStats and is a number
    const hitRate = cacheStats.basicCacheStats?.hitRate as number;
    if (hitRate !== undefined && hitRate < 0.8) {
      recommendations.performanceIssues.push(
        `Low cache hit rate: ${(hitRate * 100).toFixed(1)}%`
      );
      recommendations.suggestedStrategies.push('predictive_content');
    }

    // Analyze strategy performance
    Object.entries(this.stats.strategiesPerformance).forEach(([name, stats]) => {
      if (stats.successRate < 0.9) {
        recommendations.performanceIssues.push(
          `Strategy '${name}' has low success rate: ${(stats.successRate * 100).toFixed(1)}%`
        );
      }

      if (stats.cacheHitImprovement > 0.1) {
        recommendations.optimizationOpportunities.push(
          `Strategy '${name}' shows good performance, consider increasing frequency`
        );
      }
    });

    // Check for unused strategies
    const unusedStrategies = Array.from(this.strategies.keys()).filter(name =>
      this.stats.strategiesPerformance[name].executions === 0
    );

    if (unusedStrategies.length > 0) {
      recommendations.suggestedStrategies.push(...unusedStrategies);
    }

    return recommendations;
  }

  // Private implementation methods

  private scheduleWarmingJobs(): void {
    setInterval(async () => {
      if (!this.isRunning) return;

      for (const [name, strategy] of this.strategies.entries()) {
        if (!strategy.enabled || !strategy.schedule) continue;

        // Simple cron-like scheduling (simplified for this example)
        if (this.shouldRunScheduledStrategy(strategy)) {
          await this.warmStrategy(name);
        }
      }
    }, 60000); // Check every minute
  }

  private startWarmingWorker(): void {
    // This would integrate with the JobQueue system
    // For now, we'll use a simple interval-based approach
    setInterval(async () => {
      if (!this.isRunning) return;

      // Process any pending warming jobs
      for (const [jobId, job] of this.activeJobs.entries()) {
        if (job.status === 'pending') {
          job.status = 'running';

          try {
            const strategy = this.strategies.get(job.strategy);
            if (strategy) {
              const result = await this.executeWarmingTarget(job.target);
              job.result = result;
              job.status = result.success ? 'completed' : 'failed';
            }
          } catch (error) {
            job.status = 'failed';
            job.result = {
              success: false,
              cacheKey: job.target.cacheKey || 'unknown',
              dataSize: 0,
              executionTime: 0,
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }

          // Clean up completed jobs
          if (job.status === 'completed' || job.status === 'failed') {
            this.activeJobs.delete(jobId);
          }
        }
      }
    }, 5000); // Process every 5 seconds
  }

  private startPerformanceMonitoring(): void {
    setInterval(async () => {
      if (!this.isRunning) return;

      // Monitor cache performance and trigger adaptive warming
      const cacheStats = await redisCache.getStats();
      // Check if hitRate exists in cacheStats and is a number
      const hitRate = cacheStats.basicCacheStats?.hitRate as number;

      if (hitRate !== undefined && hitRate < 0.7) {
        // Low hit rate, trigger aggressive warming
        await this.warmStrategy('popular_polls');
      }

      // Monitor for high-miss cache keys
      const highMissKeys = await this.identifyHighMissKeys();
      for (const key of highMissKeys) {
        await this.warmSpecificKey(key);
      }
    }, 300000); // Monitor every 5 minutes
  }

  private async executeWarmingTarget(
    target: WarmingTarget
  ): Promise<WarmingResult> {
    const startTime = Date.now();

    try {
      let cacheKey: string;
      let data: unknown;

      switch (target.type) {
        case 'query':
          const result = await this.warmQuery(target);
          cacheKey = result.cacheKey;
          data = result.data;
          break;

        case 'page':
          cacheKey = target.cacheKey!;
          data = await this.warmPage(target);
          break;

        case 'computation':
          cacheKey = target.cacheKey!;
          data = await this.warmComputation(target);
          break;

        case 'api_response':
          cacheKey = target.cacheKey!;
          data = await this.warmApiResponse(target);
          break;

        default:
          throw new Error(`Unknown warming target type: ${target.type}`);
      }

      // Store in cache
      await redisCache.set(cacheKey, data, target.ttl);

      return {
        success: true,
        cacheKey,
        dataSize: JSON.stringify(data).length,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        success: false,
        cacheKey: target.cacheKey || 'unknown',
        dataSize: 0,
        executionTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async warmQuery(target: WarmingTarget): Promise<{ cacheKey: string; data: unknown }> {
    const { table, query, parameters = {} } = target;

    switch (query) {
      case 'popular_recent':
        const popularPolls = await DatabasePaginator.paginateWithOffset(
          table!,
          {
            pageSize: parameters.limit ? Number(parameters.limit) : 50,
            sortBy: 'created_at',
            sortOrder: 'desc',
            filters: {
              created_at: `gte.${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()}`
            }
          }
        );
        return {
          cacheKey: `polls:popular:${parameters.timeframe || '7d'}`,
          data: popularPolls
        };

      case 'active_users_dashboard_data':
        const { data: profiles } = await supabase
          .from('profiles')
          .select('*')
          .eq('is_active', true)
          .limit(100);

        return {
          cacheKey: 'users:dashboard_data',
          data: profiles
        };

      case 'recent_with_stats':
        const recentPolls = await DatabasePaginator.paginateWithOffset(
          table!,
          {
            pageSize: parameters.limit ? Number(parameters.limit) : 100,
            sortBy: 'created_at',
            sortOrder: 'desc'
          }
        );
        return {
          cacheKey: 'polls:recent_with_stats',
          data: recentPolls
        };

      default:
        throw new Error(`Unknown query type: ${query}`);
    }
  }

  private async warmPage(target: WarmingTarget): Promise<unknown> {
    // Simulate page data warming
    // In a real implementation, this would render or fetch page-specific data
    return {
      timestamp: Date.now(),
      content: 'warmed_page_data',
      key: target.cacheKey
    };
  }

  private async warmComputation(target: WarmingTarget): Promise<unknown> {
    // Simulate expensive computation warming
    // In a real implementation, this would perform actual computations
    return {
      timestamp: Date.now(),
      computation: 'complex_calculation_result',
      cost: target.estimatedCost || 1
    };
  }

  private async warmApiResponse(target: WarmingTarget): Promise<unknown> {
    // Simulate API response warming
    return {
      timestamp: Date.now(),
      response: 'api_response_data',
      endpoint: target.cacheKey
    };
  }

  private async evaluateConditions(conditions: WarmingCondition[]): Promise<boolean> {
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition);
      if (!result) return false;
    }
    return true;
  }

  private async evaluateCondition(condition: WarmingCondition): Promise<boolean> {
    switch (condition.type) {
      case 'time':
        return this.isInTimeRange(condition.timeRange || '');

      case 'cache_miss':
        const missCount = await this.getCacheMissCount(condition.timeRange || '1h');
        return this.compareValue(missCount, condition.threshold || 0, condition.comparator || 'gte');

      case 'usage':
        const usageCount = await this.getUsageCount(condition.timeRange || '5m');
        return this.compareValue(usageCount, condition.threshold || 0, condition.comparator || 'gte');

      case 'data_change':
        const changeCount = await this.getDataChangeCount();
        return this.compareValue(changeCount, condition.threshold || 0, condition.comparator || 'gte');

      default:
        return true;
    }
  }

  private shouldRunScheduledStrategy(strategy: WarmingStrategy): boolean {
    // Simplified cron evaluation - in production, use a proper cron library
    if (!strategy.schedule) return false;

    // For demo purposes, run every strategy once per hour
    const strategyStats = this.stats.strategiesPerformance[strategy.name];
    const lastRun = strategyStats?.lastExecution || 0;
    const hoursSinceLastRun = (Date.now() - lastRun) / (1000 * 60 * 60);

    return hoursSinceLastRun >= 1;
  }

  private updateStrategyStats(strategyName: string, results: WarmingResult[]): void {
    const stats = this.stats.strategiesPerformance[strategyName];

    stats.executions++;
    stats.lastExecution = Date.now();

    const successCount = results.filter(r => r.success).length;
    stats.successRate = (stats.successRate * (stats.executions - 1) + successCount / results.length) / stats.executions;

    const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
    this.stats.averageExecutionTime = (this.stats.averageExecutionTime + avgExecutionTime) / 2;
  }

  private getPriorityScore(priority: string): number {
    switch (priority) {
      case 'critical': return 10;
      case 'high': return 7;
      case 'medium': return 5;
      case 'low': return 2;
      default: return 1;
    }
  }

  private isInTimeRange(timeRange: string): boolean {
    const now = new Date();

    switch (timeRange) {
      case 'peak_hours':
        const hour = now.getHours();
        return (hour >= 8 && hour <= 11) || (hour >= 17 && hour <= 21);

      case 'off_hours':
        const offHour = now.getHours();
        return offHour < 6 || offHour > 23;

      default:
        return true;
    }
  }

  private compareValue(actual: number, threshold: number, comparator: string): boolean {
    switch (comparator) {
      case 'gt': return actual > threshold;
      case 'lt': return actual < threshold;
      case 'eq': return actual === threshold;
      case 'gte': return actual >= threshold;
      case 'lte': return actual <= threshold;
      default: return true;
    }
  }

  private async getCacheMissCount(timeRange: string): Promise<number> {
    // Simplified - in practice, would query performance metrics based on timeRange
    console.log(`Getting cache miss count for time range: ${timeRange}`);
    return Math.floor(Math.random() * 10);
  }

  private async getUsageCount(timeRange: string): Promise<number> {
    // Simplified - in practice, would query usage metrics based on timeRange
    console.log(`Getting usage count for time range: ${timeRange}`);
    return Math.floor(Math.random() * 20);
  }

  private async getDataChangeCount(): Promise<number> {
    // Simplified - in practice, would query change logs
    return Math.floor(Math.random() * 50);
  }

  private async identifyHighMissKeys(): Promise<string[]> {
    // Simplified - in practice, would analyze cache miss patterns
    return ['polls:trending', 'users:active', 'api:stats'];
  }

  private async warmSpecificKey(key: string): Promise<void> {
    // Implement specific key warming logic
    console.log(`Warming specific key: ${key}`);
  }
}

// Export singleton instance
export const cacheWarmer = new CacheWarmer();

// Export utility functions
export const startCacheWarming = () => cacheWarmer.start();
export const stopCacheWarming = () => cacheWarmer.stop();
export const triggerWarmingEvent = (eventName: string) =>
  cacheWarmer.warmOnEvent(eventName);
export const getWarmingStats = () => cacheWarmer.getStats();
export const getWarmingRecommendations = () => cacheWarmer.getRecommendations();
