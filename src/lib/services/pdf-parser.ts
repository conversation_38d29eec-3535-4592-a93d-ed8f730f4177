'use server';

import pdfParse from 'pdf-parse/lib/pdf-parse.js';

/**
 * Custom wrapper for pdf-parse that avoids the issue with the library's debug mode
 * @param buffer PDF file buffer
 * @returns Parsed PDF data
 */
export async function parsePdf(buffer: Buffer) {
  try {
    // Use the direct import from pdf-parse/lib/pdf-parse.js to avoid the debug mode issue
    const data = await pdfParse(buffer);
    return data;
  } catch (error) {
    console.error('Error in PDF parsing:', error);
    throw error;
  }
}
