import Redis, { Redis as RedisType } from 'ioredis';
import { createHash } from 'crypto';
import { LRUCache } from 'lru-cache';
import { cacheService as fallbackCache } from './cache-service';

/**
 * Enhanced Redis-based cache service with fallback to in-memory cache
 * Provides production-scale caching with compression and smart invalidation
 */
class RedisCacheService {
  private redis: RedisType | null = null;
  private fallbackCache: LRUCache<string, unknown>;
  private isRedisAvailable = false;
  private compressionEnabled = true;
  private readonly maxRetries = 3;
  private readonly defaultTTL = 24 * 60 * 60; // 24 hours in seconds

  constructor() {
    // Initialize fallback LRU cache with larger capacity
    this.fallbackCache = new LRUCache({
      max: 1000,
      ttl: this.defaultTTL * 1000, // LRU cache expects milliseconds
      allowStale: false,
      updateAgeOnGet: true,
      updateAgeOnHas: true,
    });

    this.initializeRedis();
  }

  /**
   * Initialize Redis connection with error handling
   */
  private async initializeRedis(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || process.env.UPSTASH_REDIS_REST_URL;

      if (!redisUrl) {
        console.log('Redis URL not found, using fallback cache only');
        return;
      }

      this.redis = new Redis(redisUrl, {
        maxRetriesPerRequest: this.maxRetries,
        lazyConnect: true,
        keepAlive: 30000,
        enableReadyCheck: true,
        enableOfflineQueue: false,
        connectTimeout: 10000,
        retryStrategy: (times) => Math.min(times * 100, 3000)
      });

      // Test connection
      await this.redis.ping();
      this.isRedisAvailable = true;
      console.log('Redis cache service initialized successfully');

      // Handle Redis errors gracefully
      this.redis.on('error', (error) => {
        console.error('Redis error:', error);
        this.isRedisAvailable = false;
      });

      this.redis.on('connect', () => {
        console.log('Redis connected');
        this.isRedisAvailable = true;
      });

      this.redis.on('ready', () => {
        console.log('Redis ready');
        this.isRedisAvailable = true;
      });

    } catch (error) {
      console.error('Failed to initialize Redis:', error);
      this.isRedisAvailable = false;
    }
  }

  /**
   * Generate cache key with namespace and versioning
   */
  generateKey(namespace: string, identifier: string, version = 'v1'): string {
    const hash = createHash('md5').update(identifier).digest('hex');
    return `pollgpt:${version}:${namespace}:${hash}`;
  }

  /**
   * Get value from cache with fallback strategy
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try Redis first if available
      if (this.isRedisAvailable && this.redis) {
        const value = await this.redis.get(key);
        if (value !== null) {
          return this.deserializeValue<T>(value);
        }
      }

      // Fallback to LRU cache
      const fallbackValue = this.fallbackCache.get(key);
      if (fallbackValue !== undefined) {
        return fallbackValue as T;
      }

      // Final fallback to basic cache service
      return fallbackCache.get<T>(key);
    } catch (error) {
      console.error('Cache get error:', error);
      // Try fallback cache
      return this.fallbackCache.get(key) as T || fallbackCache.get<T>(key);
    }
  }
  
  /**
   * Batch get multiple values from cache
   * Returns a map of key to value for all found keys
   */
  async batchGet<T>(keys: string[]): Promise<Record<string, T>> {
    try {
      const results: Record<string, T> = {};
      
      // Try Redis first if available (using mget for efficiency)
      if (this.isRedisAvailable && this.redis && keys.length > 0) {
        const values = await this.redis.mget(...keys);
        
        // Process results
        for (let i = 0; i < keys.length; i++) {
          const value = values[i];
          if (value !== null) {
            results[keys[i]] = this.deserializeValue<T>(value);
          }
        }
      }
      
      // For any missing keys, check fallback caches
      const missingKeys = keys.filter(key => !results[key]);
      
      for (const key of missingKeys) {
        // Check LRU cache
        const fallbackValue = this.fallbackCache.get(key);
        if (fallbackValue !== undefined) {
          results[key] = fallbackValue as T;
          continue;
        }
        
        // Check basic cache service
        const basicValue = fallbackCache.get<T>(key);
        if (basicValue !== null) {
          results[key] = basicValue;
        }
      }
      
      return results;
    } catch (error) {
      console.error('Batch cache get error:', error);
      // Fallback to individual gets
      const results: Record<string, T> = {};
      
      for (const key of keys) {
        const value = this.fallbackCache.get(key) as T || fallbackCache.get<T>(key);
        if (value !== null && value !== undefined) {
          results[key] = value;
        }
      }
      
      return results;
    }
  }

  /**
   * Set value in cache with multi-tier storage
   */
  async set<T>(key: string, value: T, ttl: number = this.defaultTTL): Promise<boolean> {
    try {
      const serializedValue = this.serializeValue(value);

      // Store in Redis if available
      if (this.isRedisAvailable && this.redis) {
        await this.redis.setex(key, ttl, serializedValue);
      }

      // Always store in fallback caches for immediate access
      this.fallbackCache.set(key, value, { ttl: ttl * 1000 });
      fallbackCache.set(key, value, ttl * 1000);

      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      // At least try to store in fallback
      this.fallbackCache.set(key, value, { ttl: ttl * 1000 });
      return false;
    }
  }

  /**
   * Delete value from all cache tiers
   */
  async delete(key: string): Promise<boolean> {
    try {
      let success = false;

      if (this.isRedisAvailable && this.redis) {
        const result = await this.redis.del(key);
        success = result > 0;
      }

      // Remove from fallback caches
      this.fallbackCache.delete(key);
      fallbackCache.delete(key);

      return success;
    } catch (error) {
      console.error('Cache delete error:', error);
      this.fallbackCache.delete(key);
      return false;
    }
  }

  /**
   * Delete multiple keys with pattern matching
   */
  async deletePattern(pattern: string): Promise<number> {
    try {
      let deletedCount = 0;

      if (this.isRedisAvailable && this.redis) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      }

      // Clear fallback caches (they don't support pattern matching, so clear all)
      if (pattern.includes('*')) {
        this.fallbackCache.clear();
        fallbackCache.clear();
      }

      return deletedCount;
    } catch (error) {
      console.error('Cache pattern delete error:', error);
      return 0;
    }
  }

  /**
   * Check if a key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      if (this.isRedisAvailable && this.redis) {
        const exists = await this.redis.exists(key);
        return exists === 1;
      }

      return this.fallbackCache.has(key) || fallbackCache.get(key) !== null;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * Set cache entry with expiration time
   */
  async setWithExpiry<T>(key: string, value: T, expiryDate: Date): Promise<boolean> {
    const ttl = Math.max(0, Math.floor((expiryDate.getTime() - Date.now()) / 1000));
    return this.set(key, value, ttl);
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    redisAvailable: boolean;
    fallbackCacheSize: number;
    fallbackCacheStats: Record<string, number>;
    basicCacheStats: Record<string, unknown>;
  }> {
    try {
      return {
        redisAvailable: this.isRedisAvailable,
        fallbackCacheSize: this.fallbackCache.size,
        fallbackCacheStats: {
          size: this.fallbackCache.size,
          max: this.fallbackCache.max,
          calculatedSize: this.fallbackCache.calculatedSize,
        },
        basicCacheStats: fallbackCache.getStats(),
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        redisAvailable: false,
        fallbackCacheSize: 0,
        fallbackCacheStats: {},
        basicCacheStats: {},
      };
    }
  }

  /**
   * Serialize value for storage with optional compression
   */
  private serializeValue<T>(value: T): string {
    try {
      const jsonString = JSON.stringify(value);

      if (this.compressionEnabled && jsonString.length > 1024) {
        // For large objects, we could add compression here
        // For now, just return the JSON string
        return jsonString;
      }

      return jsonString;
    } catch (error) {
      console.error('Serialization error:', error);
      return JSON.stringify(value);
    }
  }

  /**
   * Deserialize value from storage
   */
  private deserializeValue<T>(value: string): T | null {
    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Deserialization error:', error);
      return null;
    }
  }

  /**
   * Clean up expired entries and optimize cache
   */
  async cleanup(): Promise<{ redisCleanedKeys: number; fallbackCleaned: boolean }> {
    let redisCleanedKeys = 0;

    try {
      // Redis automatically handles TTL, but we can clean up explicitly if needed
      if (this.isRedisAvailable && this.redis) {
        // Get all keys that match our pattern and check their TTL
        const keys = await this.redis.keys('pollgpt:*');
        const expiredKeys = [];

        for (const key of keys) {
          const ttl = await this.redis.ttl(key);
          if (ttl === -2) { // Key doesn't exist
            expiredKeys.push(key);
          }
        }

        if (expiredKeys.length > 0) {
          redisCleanedKeys = await this.redis.del(...expiredKeys);
        }
      }

      // LRU cache automatically handles cleanup, but we can trigger it
      // Force cleanup of expired items
      this.fallbackCache.purgeStale();

      // Clean up basic cache
      fallbackCache.cleanExpired();

      return {
        redisCleanedKeys,
        fallbackCleaned: true,
      };
    } catch (error) {
      console.error('Cache cleanup error:', error);
      return {
        redisCleanedKeys: 0,
        fallbackCleaned: false,
      };
    }
  }

  /**
   * Close Redis connection gracefully
   */
  async close(): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.quit();
        this.isRedisAvailable = false;
      }
    } catch (error) {
      console.error('Error closing Redis connection:', error);
    }
  }

  /**
   * Batch operations for better performance
   */
  async batchSet<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean> {
    try {
      if (this.isRedisAvailable && this.redis) {
        const pipeline = this.redis.pipeline();

        entries.forEach(({ key, value, ttl = this.defaultTTL }) => {
          const serializedValue = this.serializeValue(value);
          pipeline.setex(key, ttl, serializedValue);
        });

        await pipeline.exec();
      }

      // Also store in fallback caches
      entries.forEach(({ key, value, ttl = this.defaultTTL }) => {
        this.fallbackCache.set(key, value, { ttl: ttl * 1000 });
        fallbackCache.set(key, value, ttl * 1000);
      });

      return true;
    } catch (error) {
      console.error('Batch set error:', error);
      return false;
    }
  }

  // The batchGet function is already implemented above
}

// Create singleton instance
const redisCacheService = new RedisCacheService();

// Export class and instance for named imports
export { RedisCacheService, redisCacheService };

// Also export as default for modules that use default import
export default redisCacheService;

// Export type for RedisCache to fix import errors
export type RedisCache = RedisCacheService;

/**
 * Enhanced cache wrapper with automatic key generation and namespace support
 */
export async function withEnhancedCache<T>(
  namespace: string,
  identifier: string,
  fn: () => Promise<T>,
  ttl: number = 24 * 60 * 60, // 24 hours
  version = 'v1'
): Promise<T> {
  const cacheKey = redisCacheService.generateKey(namespace, identifier, version);

  // Check cache first
  const cachedValue = await redisCacheService.get<T>(cacheKey);
  if (cachedValue !== null) {
    return cachedValue;
  }

  // Cache miss, run the function
  const result = await fn();

  // Store in cache
  await redisCacheService.set(cacheKey, result, ttl);

  return result;
}

/**
 * Cache invalidation helper for simulation-related caches
 */
export async function invalidateSimulationCache(
  pollQuestion?: string,
  demographic?: string
): Promise<void> {
  try {
    if (pollQuestion && demographic) {
      // Invalidate specific simulation
      const identifier = `${pollQuestion}:${demographic}`;
      const cacheKey = redisCacheService.generateKey('simulation', identifier);
      await redisCacheService.delete(cacheKey);
    } else if (pollQuestion) {
      // Invalidate all simulations for a question
      const pattern = `pollgpt:*:simulation:*${createHash('md5').update(pollQuestion).digest('hex').substring(0, 8)}*`;
      await redisCacheService.deletePattern(pattern);
    } else {
      // Invalidate all simulation caches
      await redisCacheService.deletePattern('pollgpt:*:simulation:*');
    }
  } catch (error) {
    console.error('Cache invalidation error:', error);
  }
}
