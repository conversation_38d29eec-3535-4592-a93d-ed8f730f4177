"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
// Helper function to generate a unique ID
const generateId = function () {
    return uuidv4();
};
// Get all polls for the current user with enhanced error handling
export const getPolls = async function () {
    var connectionStart, _a, user, userError, connectionTime, fetchStart, _b, data, error, fetchTime, pollIds, _c, responseData, responseError, responseCountMap_1, _d, questionData, questionError, questionsByPoll_1, polls, error_1;
    return __generator(this, function (_e) {
        switch (_e.label) {
            case 0:
                _e.trys.push([0, 5, , 6]);
                connectionStart = Date.now();
                return [4 /*yield*/, Promise.race([
                        supabase_1.supabase.auth.getUser(),
                        new Promise(function (_, reject) {
                            return setTimeout(function () { return reject(new Error("Auth connection timed out")); }, 10000);
                        })
                    ])];
            case 1:
                _a = _e.sent(), user = _a.data.user, userError = _a.error;
                connectionTime = Date.now() - connectionStart;
                console.log("Auth connection response time: ".concat(connectionTime, "ms"));
                if (userError) {
                    console.error("Error fetching user:", userError);
                    throw new Error("User authentication error: ".concat(userError.message));
                }
                if (!user) {
                    console.warn("No authenticated user found when fetching polls");
                    return [2 /*return*/, []];
                }
                console.log("Fetching polls for user:", user.id);
                fetchStart = Date.now();
                return [4 /*yield*/, Promise.race([
                        supabase_1.supabase
                            .from('polls')
                            .select('id, title, description, created_at, updated_at, user_id, is_published, is_public, slug')
                            .eq('user_id', user.id)
                            .order('updated_at', { ascending: false }),
                        new Promise(function (_, reject) {
                            return setTimeout(function () { return reject(new Error("Database query timed out")); }, 15000);
                        } // Increased from 8s to 15s
                        )
                    ])];
            case 2:
                _b = _e.sent(), data = _b.data, error = _b.error;
                fetchTime = Date.now() - fetchStart;
                console.log("Poll fetch response time: ".concat(fetchTime, "ms"));
                if (error) {
                    console.error("Error fetching polls:", error);
                    // Add more diagnostic information for debugging
                    if (error.message.includes('RLS') || error.message.includes('permission denied')) {
                        console.error("RLS policy might be blocking access. Check database policies.");
                        throw new Error("Database access denied. Your session might have expired. Please try logging out and back in.");
                    }
                    else {
                        throw new Error("Database error: ".concat(error.message));
                    }
                }
                // Handle null/undefined data case
                if (!data) {
                    console.log("No poll data returned");
                    return [2 /*return*/, []]; // Return empty array if no data
                }
                console.log("Found ".concat(data.length || 0, " polls"));
                pollIds = data.map(function (poll) { return poll.id; });
                return [4 /*yield*/, supabase_1.supabase
                        .from('responses')
                        .select('poll_id')
                        .in('poll_id', pollIds)];
            case 3:
                _c = _e.sent(), responseData = _c.data, responseError = _c.error;
                if (responseError) {
                    console.warn("Error fetching responses:", responseError);
                }
                responseCountMap_1 = {};
                if (responseData && Array.isArray(responseData)) {
                    // Initialize all poll IDs with 0 counts
                    pollIds.forEach(function (id) { responseCountMap_1[id] = 0; });
                    // Count responses for each poll
                    responseData.forEach(function (item) {
                        if (item.poll_id) {
                            responseCountMap_1[item.poll_id] = (responseCountMap_1[item.poll_id] || 0) + 1;
                        }
                    });
                }
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .select('id, poll_id, question_text, question_type')
                        .in('poll_id', pollIds)];
            case 4:
                _d = _e.sent(), questionData = _d.data, questionError = _d.error;
                if (questionError) {
                    console.warn("Error fetching questions:", questionError);
                }
                questionsByPoll_1 = {};
                if (questionData && Array.isArray(questionData)) {
                    questionData.forEach(function (q) {
                        if (!questionsByPoll_1[q.poll_id]) {
                            questionsByPoll_1[q.poll_id] = [];
                        }
                        questionsByPoll_1[q.poll_id].push(q);
                    });
                }
                polls = data.map(function (dbPoll) {
                    // Get response count from our map
                    var responsesCount = responseCountMap_1[dbPoll.id] || 0;
                    // Get minimal question data
                    var pollQuestions = questionsByPoll_1[dbPoll.id] || [];
                    var questions = pollQuestions.map(function (q) { return ({
                        id: q.id,
                        text: q.question_text,
                        type: q.question_type,
                        options: [], // We don't need options for the polls list view
                        required: false, // Default value
                        order: 0 // Default value
                    }); });
                    return {
                        id: dbPoll.id,
                        title: dbPoll.title || 'Untitled Poll',
                        description: dbPoll.description || '',
                        questions: questions,
                        createdAt: dbPoll.created_at,
                        updatedAt: dbPoll.updated_at || dbPoll.created_at,
                        expiresAt: null, // No expires_at column in the database
                        userId: dbPoll.user_id,
                        status: dbPoll.is_published ? 'active' : 'draft',
                        responses: responsesCount,
                        views: 0, // No views column in the database
                        is_public: dbPoll.is_public !== undefined ? dbPoll.is_public : true,
                        access_code: undefined // No access_code column in the database
                    };
                });
                return [2 /*return*/, polls];
            case 5:
                error_1 = _e.sent();
                console.error("Error fetching polls:", error_1);
                // Always return an empty array instead of null/undefined
                return [2 /*return*/, []];
            case 6: return [2 /*return*/];
        }
    });
}); };
exports.getPolls = getPolls;
// Get a specific poll by ID
var getPollById = function (id_1) {
    var args_1 = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        args_1[_i - 1] = arguments[_i];
    }
    return __awaiter(void 0, __spreadArray([id_1], args_1, true), void 0, function (id, preview) {
        var startTime, timeoutPromise, query, _a, pollData, pollError, fetchTime, questionsTimeoutPromise, _b, questionsData, questionsError, questions, countTimeoutPromise, _c, responseCount, countError, poll, error_2;
        if (preview === void 0) { preview = false; }
        return __generator(this, function (_d) {
            switch (_d.label) {
                case 0:
                    _d.trys.push([0, 4, , 5]);
                    console.log("Fetching poll with ID: ".concat(id, ", preview mode: ").concat(preview));
                    startTime = Date.now();
                    timeoutPromise = new Promise(function (resolve) {
                        return setTimeout(function () { return resolve({ data: null, error: new Error("Poll fetch timed out for id: ".concat(id)) }); }, 15000);
                    });
                    query = supabase_1.supabase
                        .from('polls')
                        .select('*')
                        .eq('id', id);
                    // If not in preview mode, only get published and public polls
                    if (!preview) {
                        query = query.eq('is_published', true).eq('is_public', true);
                    }
                    return [4 /*yield*/, Promise.race([
                            query.single(),
                            timeoutPromise
                        ])];
                case 1:
                    _a = _d.sent(), pollData = _a.data, pollError = _a.error;
                    fetchTime = Date.now() - startTime;
                    console.log("Poll fetch time: ".concat(fetchTime, "ms for ID: ").concat(id));
                    if (pollError) {
                        console.error("Error fetching poll ".concat(id, ":"), pollError);
                        return [2 /*return*/, null];
                    }
                    if (!pollData) {
                        console.log("Poll not found with ID: ".concat(id));
                        return [2 /*return*/, null];
                    }
                    questionsTimeoutPromise = new Promise(function (resolve) {
                        return setTimeout(function () { return resolve({ data: null, error: new Error("Questions fetch timed out for poll: ".concat(id)) }); }, 10000);
                    });
                    return [4 /*yield*/, Promise.race([
                            supabase_1.supabase
                                .from('questions')
                                .select('*')
                                .eq('poll_id', id)
                                .order('order', { ascending: true }),
                            questionsTimeoutPromise
                        ])];
                case 2:
                    _b = _d.sent(), questionsData = _b.data, questionsError = _b.error;
                    if (questionsError) {
                        console.error("Error fetching questions for poll ".concat(id, ":"), questionsError);
                        // Continue anyway, just with no questions
                    }
                    questions = (questionsData === null || questionsData === void 0 ? void 0 : questionsData.map(function (q) { return ({
                        id: q.id,
                        text: q.question_text,
                        type: q.question_type,
                        options: q.options || [],
                        required: q.required,
                        order: q.order
                    }); })) || [];
                    countTimeoutPromise = new Promise(function (resolve) {
                        return setTimeout(function () { return resolve({ count: null, error: new Error("Response count timed out for poll: ".concat(id)) }); }, 8000);
                    });
                    return [4 /*yield*/, Promise.race([
                            supabase_1.supabase
                                .from('responses')
                                .select('*', { count: 'exact', head: true })
                                .eq('poll_id', id),
                            countTimeoutPromise
                        ])];
                case 3:
                    _c = _d.sent(), responseCount = _c.count, countError = _c.error;
                    if (countError) {
                        console.error("Error counting poll responses:", countError);
                    }
                    poll = {
                        id: pollData.id,
                        title: pollData.title,
                        description: pollData.description || '',
                        createdAt: pollData.created_at,
                        updatedAt: pollData.updated_at || pollData.created_at,
                        expiresAt: null,
                        questions: questions,
                        userId: pollData.user_id,
                        status: pollData.is_published ? 'active' : 'draft',
                        responses: responseCount || 0,
                        views: 0, // This might need to be tracked separately
                        is_public: pollData.is_public !== undefined ? pollData.is_public : true,
                        access_code: pollData.access_code || undefined
                    };
                    return [2 /*return*/, poll];
                case 4:
                    error_2 = _d.sent();
                    console.error("Error fetching poll by ID:", error_2);
                    return [2 /*return*/, null];
                case 5: return [2 /*return*/];
            }
        });
    });
};
exports.getPollById = getPollById;
// Create a new poll
var createPoll = function (pollData) { return __awaiter(void 0, void 0, void 0, function () {
    var user, pollId_1, now_1, dbPoll, pollError, questionsToInsert, questionsError, createdPoll, error_3;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 5, , 6]);
                return [4 /*yield*/, supabase_1.supabase.auth.getUser()];
            case 1:
                user = (_a.sent()).data.user;
                if (!user) {
                    throw new Error("User must be authenticated to create a poll");
                }
                console.log('Authenticated user:', user.id);
                pollId_1 = generateId();
                now_1 = new Date().toISOString();
                dbPoll = {
                    id: pollId_1,
                    title: pollData.title,
                    description: pollData.description || null,
                    user_id: user.id,
                    is_published: true, // Default to published
                    is_public: true, // Default to public
                    created_at: now_1,
                    updated_at: now_1,
                    slug: pollId_1.substring(0, 8) // Create a short slug from the ID
                };
                console.log('Creating poll with data:', dbPoll);
                return [4 /*yield*/, supabase_1.supabase
                        .from('polls')
                        .insert(dbPoll)];
            case 2:
                pollError = (_a.sent()).error;
                if (pollError) {
                    console.error("Error inserting poll:", pollError);
                    throw pollError;
                }
                console.log('Poll created successfully, now adding questions');
                if (!(pollData.questions && pollData.questions.length > 0)) return [3 /*break*/, 4];
                questionsToInsert = pollData.questions.map(function (q) { return ({
                    id: generateId(), // Use UUID for question ID
                    poll_id: pollId_1,
                    question_text: q.text,
                    question_type: q.type,
                    options: q.options || null,
                    required: q.required,
                    order: q.order,
                    created_at: now_1
                }); });
                console.log("Inserting ".concat(questionsToInsert.length, " questions"));
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .insert(questionsToInsert)];
            case 3:
                questionsError = (_a.sent()).error;
                if (questionsError) {
                    console.error("Error inserting questions:", questionsError);
                    throw questionsError;
                }
                console.log('Questions inserted successfully');
                _a.label = 4;
            case 4:
                createdPoll = {
                    id: pollId_1,
                    title: pollData.title,
                    description: pollData.description || '',
                    createdAt: now_1,
                    updatedAt: now_1,
                    expiresAt: pollData.expiresAt || null,
                    questions: pollData.questions || [],
                    userId: user.id,
                    status: 'active',
                    responses: 0,
                    views: 0,
                    is_public: pollData.is_public
                };
                console.log('Poll creation complete:', createdPoll.id);
                return [2 /*return*/, createdPoll];
            case 5:
                error_3 = _a.sent();
                console.error("Error creating poll:", error_3);
                sonner_1.toast.error("Failed to create poll. Please try again.");
                throw error_3;
            case 6: return [2 /*return*/];
        }
    });
}); };
exports.createPoll = createPoll;
// Update an existing poll
var updatePoll = function (id, pollData) { return __awaiter(void 0, void 0, void 0, function () {
    var now, totalStartTime, isOnlyUpdatingViews, dbUpdateData, updateQuery, user, authStartTime, _a, userData, userError, authEndTime, pollUpdateStartTime, _b, data, error, pollUpdateEndTime, updatedQuestions, questionsStartTime, _c, existingQuestions, fetchError, existingQuestionsMap_1, questionsToUpdate, questionsToInsert, existingIds, updatedIds_1, _i, updatedQuestions_1, question, dbQuestion, questionsToDelete, insertStartTime, insertError, insertEndTime, updateStartTime, updateError, updateEndTime, deleteStartTime, deleteError, deleteEndTime, questionsEndTime, questionError_1, updatedPoll, totalEndTime, error_4;
    return __generator(this, function (_d) {
        switch (_d.label) {
            case 0:
                _d.trys.push([0, 14, , 15]);
                console.log("[updatePoll] Starting update for poll ID: ".concat(id, " at ").concat(new Date().toISOString()));
                now = new Date().toISOString();
                totalStartTime = Date.now();
                isOnlyUpdatingViews = Object.keys(pollData).length === 1 && pollData.views !== undefined;
                dbUpdateData = {};
                if (pollData.title !== undefined)
                    dbUpdateData.title = pollData.title;
                if (pollData.description !== undefined)
                    dbUpdateData.description = pollData.description;
                if (pollData.status !== undefined)
                    dbUpdateData.is_published = pollData.status === 'active';
                if (pollData.is_public !== undefined)
                    dbUpdateData.is_public = pollData.is_public;
                if (pollData.views !== undefined)
                    dbUpdateData.views = pollData.views;
                // Note: access_code and expiresAt are not in the database schema
                // Always update the updated_at timestamp
                dbUpdateData.updated_at = now;
                console.log('[updatePoll] Updating poll with data:', dbUpdateData);
                updateQuery = supabase_1.supabase
                    .from('polls')
                    .update(dbUpdateData)
                    .eq('id', id);
                user = void 0;
                if (!!isOnlyUpdatingViews) return [3 /*break*/, 2];
                authStartTime = Date.now();
                return [4 /*yield*/, supabase_1.supabase.auth.getUser()];
            case 1:
                _a = _d.sent(), userData = _a.data, userError = _a.error;
                authEndTime = Date.now();
                console.log("[updatePoll] Auth check took ".concat(authEndTime - authStartTime, "ms"));
                if (userError) {
                    console.error("Error getting user:", userError);
                    throw userError;
                }
                user = userData.user;
                if (!user) {
                    throw new Error("User must be authenticated to update a poll");
                }
                // Add user_id constraint for regular updates
                updateQuery = updateQuery.eq('user_id', user.id);
                _d.label = 2;
            case 2:
                pollUpdateStartTime = Date.now();
                return [4 /*yield*/, updateQuery.select().single()];
            case 3:
                _b = _d.sent(), data = _b.data, error = _b.error;
                pollUpdateEndTime = Date.now();
                console.log("[updatePoll] Poll table update took ".concat(pollUpdateEndTime - pollUpdateStartTime, "ms"));
                if (error) {
                    console.error("Error updating poll in database:", error);
                    throw error;
                }
                updatedQuestions = pollData.questions;
                if (!(updatedQuestions && !isOnlyUpdatingViews)) return [3 /*break*/, 13];
                questionsStartTime = Date.now();
                console.log("[updatePoll] Processing ".concat(updatedQuestions.length, " questions for poll ").concat(id));
                sonner_1.toast.loading("Saving questions...");
                _d.label = 4;
            case 4:
                _d.trys.push([4, 12, , 13]);
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .select('*')
                        .eq('poll_id', id)];
            case 5:
                _c = _d.sent(), existingQuestions = _c.data, fetchError = _c.error;
                if (fetchError) {
                    console.error("Error fetching existing questions:", fetchError);
                    throw fetchError;
                }
                existingQuestionsMap_1 = new Map();
                existingQuestions === null || existingQuestions === void 0 ? void 0 : existingQuestions.forEach(function (q) { return existingQuestionsMap_1.set(q.id, q); });
                questionsToUpdate = [];
                questionsToInsert = [];
                existingIds = new Set(existingQuestionsMap_1.keys());
                updatedIds_1 = new Set();
                // Process updated questions
                for (_i = 0, updatedQuestions_1 = updatedQuestions; _i < updatedQuestions_1.length; _i++) {
                    question = updatedQuestions_1[_i];
                    updatedIds_1.add(question.id);
                    dbQuestion = {
                        id: question.id,
                        poll_id: id,
                        question_text: question.text,
                        question_type: question.type,
                        options: question.options || null,
                        required: question.required,
                        order: question.order,
                        updated_at: now
                    };
                    // If question exists, update it; otherwise, insert it
                    if (existingIds.has(question.id)) {
                        questionsToUpdate.push(dbQuestion);
                    }
                    else {
                        // Add created_at for new questions
                        questionsToInsert.push(__assign(__assign({}, dbQuestion), { created_at: now }));
                    }
                }
                questionsToDelete = Array.from(existingIds)
                    .filter(function (id) { return !updatedIds_1.has(id); });
                if (!(questionsToInsert.length > 0)) return [3 /*break*/, 7];
                console.log("[updatePoll] Inserting ".concat(questionsToInsert.length, " new questions"));
                insertStartTime = Date.now();
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .insert(questionsToInsert)];
            case 6:
                insertError = (_d.sent()).error;
                insertEndTime = Date.now();
                console.log("[updatePoll] Question inserts took ".concat(insertEndTime - insertStartTime, "ms"));
                if (insertError) {
                    console.error("Error inserting new questions:", insertError);
                    throw insertError;
                }
                _d.label = 7;
            case 7:
                if (!(questionsToUpdate.length > 0)) return [3 /*break*/, 9];
                console.log("[updatePoll] Updating ".concat(questionsToUpdate.length, " existing questions"));
                updateStartTime = Date.now();
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .upsert(questionsToUpdate)];
            case 8:
                updateError = (_d.sent()).error;
                updateEndTime = Date.now();
                console.log("[updatePoll] Question updates took ".concat(updateEndTime - updateStartTime, "ms"));
                if (updateError) {
                    console.error("Error updating existing questions:", updateError);
                    throw updateError;
                }
                _d.label = 9;
            case 9:
                if (!(questionsToDelete.length > 0)) return [3 /*break*/, 11];
                console.log("[updatePoll] Deleting ".concat(questionsToDelete.length, " removed questions"));
                deleteStartTime = Date.now();
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .delete()
                        .in('id', questionsToDelete)];
            case 10:
                deleteError = (_d.sent()).error;
                deleteEndTime = Date.now();
                console.log("[updatePoll] Question deletions took ".concat(deleteEndTime - deleteStartTime, "ms"));
                if (deleteError) {
                    console.error("Error deleting removed questions:", deleteError);
                    throw deleteError;
                }
                _d.label = 11;
            case 11:
                questionsEndTime = Date.now();
                console.log("[updatePoll] All question operations completed in ".concat(questionsEndTime - questionsStartTime, "ms"));
                sonner_1.toast.dismiss();
                sonner_1.toast.success("Poll saved successfully");
                return [3 /*break*/, 13];
            case 12:
                questionError_1 = _d.sent();
                console.error("Error updating questions:", questionError_1);
                sonner_1.toast.dismiss();
                sonner_1.toast.error("Error saving questions");
                throw questionError_1;
            case 13:
                updatedPoll = {
                    id: data.id,
                    title: data.title,
                    description: data.description || '',
                    questions: updatedQuestions || [], // Use the updated questions we just processed
                    createdAt: data.created_at,
                    updatedAt: data.updated_at || data.created_at,
                    expiresAt: null, // No expires_at column in the database
                    userId: data.user_id,
                    status: data.is_published ? 'active' : 'draft',
                    responses: pollData.responses || 0,
                    views: data.views || 0,
                    is_public: data.is_public,
                    access_code: data.access_code || undefined
                };
                totalEndTime = Date.now();
                console.log("[updatePoll] Total update operation took ".concat(totalEndTime - totalStartTime, "ms"));
                return [2 /*return*/, updatedPoll];
            case 14:
                error_4 = _d.sent();
                console.error("Error updating poll:", error_4);
                throw error_4;
            case 15: return [2 /*return*/];
        }
    });
}); };
exports.updatePoll = updatePoll;
// Delete a poll
var deletePoll = function (id) { return __awaiter(void 0, void 0, void 0, function () {
    var user, questionsError, responsesError, error, error_5;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 5, , 6]);
                return [4 /*yield*/, supabase_1.supabase.auth.getUser()];
            case 1:
                user = (_a.sent()).data.user;
                if (!user) {
                    throw new Error("User must be authenticated to delete a poll");
                }
                return [4 /*yield*/, supabase_1.supabase
                        .from('questions')
                        .delete()
                        .eq('poll_id', id)];
            case 2:
                questionsError = (_a.sent()).error;
                if (questionsError) {
                    console.error("Error deleting questions:", questionsError);
                    throw questionsError;
                }
                return [4 /*yield*/, supabase_1.supabase
                        .from('responses')
                        .delete()
                        .eq('poll_id', id)];
            case 3:
                responsesError = (_a.sent()).error;
                if (responsesError) {
                    console.error("Error deleting responses:", responsesError);
                    throw responsesError;
                }
                return [4 /*yield*/, supabase_1.supabase
                        .from('polls')
                        .delete()
                        .eq('id', id)
                        .eq('user_id', user.id)];
            case 4:
                error = (_a.sent()).error;
                if (error) {
                    console.error("Error deleting poll from database:", error);
                    throw error;
                }
                return [2 /*return*/, true];
            case 5:
                error_5 = _a.sent();
                console.error("Error deleting poll:", error_5);
                throw error_5;
            case 6: return [2 /*return*/];
        }
    });
}); };
exports.deletePoll = deletePoll;
// Duplicate a poll
var duplicatePoll = function (pollId) { return __awaiter(void 0, void 0, void 0, function () {
    var poll, _id, pollWithoutId, duplicatedPoll, error_6;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 3, , 4]);
                return [4 /*yield*/, (0, exports.getPollById)(pollId)];
            case 1:
                poll = _a.sent();
                if (!poll) {
                    return [2 /*return*/, null];
                }
                _id = poll.id, pollWithoutId = __rest(poll, ["id"]);
                return [4 /*yield*/, (0, exports.createPoll)(__assign(__assign({}, pollWithoutId), { title: "".concat(poll.title, " (Copy)"), status: 'draft' }))];
            case 2:
                duplicatedPoll = _a.sent();
                return [2 /*return*/, duplicatedPoll];
            case 3:
                error_6 = _a.sent();
                console.error("Error duplicating poll:", error_6);
                return [2 /*return*/, null];
            case 4: return [2 /*return*/];
        }
    });
}); };
exports.duplicatePoll = duplicatePoll;
// Close a poll (mark as completed)
var closePoll = function (id) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, (0, exports.updatePoll)(id, {
                status: 'completed',
                expiresAt: new Date().toISOString()
            })];
    });
}); };
exports.closePoll = closePoll;
// Get all responses for a poll
var getPollResponses = function (pollId) { return __awaiter(void 0, void 0, void 0, function () {
    var _a, responseData, responseError, formattedResponses, error_7;
    return __generator(this, function (_b) {
        switch (_b.label) {
            case 0:
                _b.trys.push([0, 3, , 4]);
                return [4 /*yield*/, supabase_1.supabase
                        .from('responses')
                        .select('*, answers(*)')
                        .eq('poll_id', pollId)];
            case 1:
                _a = _b.sent(), responseData = _a.data, responseError = _a.error;
                if (responseError) {
                    console.error("Error fetching poll responses:", responseError);
                    throw responseError;
                }
                if (!responseData) {
                    return [2 /*return*/, []];
                }
                return [4 /*yield*/, Promise.all(responseData.map(function (response) { return __awaiter(void 0, void 0, void 0, function () {
                        var _a, answerData, answerError, responseAnswers;
                        return __generator(this, function (_b) {
                            switch (_b.label) {
                                case 0: return [4 /*yield*/, supabase_1.supabase
                                        .from('answers')
                                        .select('*')
                                        .eq('response_id', response.id)];
                                case 1:
                                    _a = _b.sent(), answerData = _a.data, answerError = _a.error;
                                    if (answerError) {
                                        console.error("Error fetching answers for response:", answerError);
                                        return [2 /*return*/, null];
                                    }
                                    responseAnswers = {};
                                    answerData === null || answerData === void 0 ? void 0 : answerData.forEach(function (answer) {
                                        try {
                                            // Try to parse as JSON in case it's an array
                                            responseAnswers[answer.question_id] = JSON.parse(answer.answer_value);
                                        }
                                        catch (_a) {
                                            // If it's not valid JSON, use as string
                                            responseAnswers[answer.question_id] = answer.answer_value;
                                        }
                                    });
                                    return [2 /*return*/, {
                                            id: response.id,
                                            pollId: response.poll_id,
                                            responses: responseAnswers,
                                            submittedAt: response.created_at,
                                            respondentInfo: response.respondent_info || {}
                                        }];
                            }
                        });
                    }); }))];
            case 2:
                formattedResponses = _b.sent();
                // Filter out any null responses (from errors)
                return [2 /*return*/, formattedResponses.filter(Boolean)];
            case 3:
                error_7 = _b.sent();
                console.error("Error fetching poll responses:", error_7);
                throw error_7;
            case 4: return [2 /*return*/];
        }
    });
}); };
exports.getPollResponses = getPollResponses;
// Add a response to a poll
var addPollResponse = function (response) { return __awaiter(void 0, void 0, void 0, function () {
    var newResponse_1, storageKey, error, answerPromises, answerResults, answerErrors, poll, error_8;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 6, , 7]);
                newResponse_1 = __assign(__assign({}, response), { id: generateId(), submittedAt: new Date().toISOString() });
                storageKey = "poll_response_".concat(response.pollId);
                localStorage.setItem(storageKey, JSON.stringify({
                    responseId: newResponse_1.id,
                    timestamp: newResponse_1.submittedAt
                }));
                return [4 /*yield*/, supabase_1.supabase
                        .from('responses')
                        .insert({
                        id: newResponse_1.id,
                        created_at: newResponse_1.submittedAt,
                        poll_id: newResponse_1.pollId,
                        user_id: null, // Allow anonymous responses
                        respondent_info: newResponse_1.respondentInfo
                    })];
            case 1:
                error = (_a.sent()).error;
                if (error) {
                    console.error("Error saving poll response to database:", error);
                    throw error;
                }
                answerPromises = Object.entries(newResponse_1.responses).map(function (_a) {
                    var questionId = _a[0], answerValue = _a[1];
                    return supabase_1.supabase
                        .from('answers')
                        .insert({
                        id: generateId(),
                        created_at: new Date().toISOString(),
                        response_id: newResponse_1.id,
                        question_id: questionId,
                        answer_value: typeof answerValue === 'string' ? answerValue : JSON.stringify(answerValue)
                    });
                });
                return [4 /*yield*/, Promise.all(answerPromises)];
            case 2:
                answerResults = _a.sent();
                answerErrors = answerResults.filter(function (result) { return result.error; });
                if (answerErrors.length > 0) {
                    console.error("Errors saving some answers:", answerErrors);
                    // Continue anyway to update the response count
                }
                return [4 /*yield*/, (0, exports.getPollById)(response.pollId)];
            case 3:
                poll = _a.sent();
                if (!poll) return [3 /*break*/, 5];
                return [4 /*yield*/, (0, exports.updatePoll)(response.pollId, {
                        responses: poll.responses + 1
                    })];
            case 4:
                _a.sent();
                _a.label = 5;
            case 5: return [2 /*return*/, newResponse_1];
            case 6:
                error_8 = _a.sent();
                console.error("Error adding poll response:", error_8);
                throw error_8;
            case 7: return [2 /*return*/];
        }
    });
}); };
exports.addPollResponse = addPollResponse;
// Check if user has already responded to a poll
var hasUserRespondedToPoll = function (pollId) {
    if (typeof window === 'undefined')
        return false; // Server-side check
    var storageKey = "poll_response_".concat(pollId);
    var storedResponse = localStorage.getItem(storageKey);
    return storedResponse !== null;
};
exports.hasUserRespondedToPoll = hasUserRespondedToPoll;
// Increment poll view count
var incrementPollViews = function (id) { return __awaiter(void 0, void 0, void 0, function () {
    var poll, now, _a, currentPollData, fetchError, currentViews, newViews, error, error_9;
    var _b;
    return __generator(this, function (_c) {
        switch (_c.label) {
            case 0:
                _c.trys.push([0, 4, , 5]);
                return [4 /*yield*/, (0, exports.getPollById)(id)];
            case 1:
                poll = _c.sent();
                if (!poll) {
                    console.log("Cannot increment views: Poll not found with ID: ".concat(id));
                    return [2 /*return*/, null];
                }
                now = new Date().toISOString();
                return [4 /*yield*/, supabase_1.supabase
                        .from('polls')
                        .select('views')
                        .eq('id', id)
                        .single()];
            case 2:
                _a = _c.sent(), currentPollData = _a.data, fetchError = _a.error;
                if (fetchError) {
                    console.error("Error fetching current view count for poll ".concat(id, ":"), fetchError);
                    // Continue with our cached view count if we can't get the current one
                }
                currentViews = ((_b = currentPollData === null || currentPollData === void 0 ? void 0 : currentPollData.views) !== null && _b !== void 0 ? _b : poll.views) || 0;
                newViews = currentViews + 1;
                return [4 /*yield*/, supabase_1.supabase
                        .from('polls')
                        .update({
                        views: newViews,
                        updated_at: now
                    })
                        .eq('id', id)];
            case 3:
                error = (_c.sent()).error;
                if (error) {
                    console.error("Error incrementing views for poll ".concat(id, ":"), error);
                    return [2 /*return*/, poll]; // Return original poll if update fails
                }
                // Return updated poll object
                return [2 /*return*/, __assign(__assign({}, poll), { views: newViews, updatedAt: now })];
            case 4:
                error_9 = _c.sent();
                console.error("Error incrementing poll views:", error_9);
                return [2 /*return*/, null];
            case 5: return [2 /*return*/];
        }
    });
}); };
exports.incrementPollViews = incrementPollViews;
// Initialize with sample data if none exists
var initializeWithSampleData = function () { return __awaiter(void 0, void 0, void 0, function () {
    var _a, user, userError, _b, existingPolls, error, samplePolls_2, _i, samplePolls_1, poll, sampleResponses, _c, sampleResponses_1, response, responseError, _d, _e, _f, questionId, answerValue, error_10;
    return __generator(this, function (_g) {
        switch (_g.label) {
            case 0:
                _g.trys.push([0, 14, , 15]);
                return [4 /*yield*/, supabase_1.supabase.auth.getUser()];
            case 1:
                _a = _g.sent(), user = _a.data.user, userError = _a.error;
                if (userError || !user) {
                    console.warn("No authenticated user found when initializing sample data");
                    return [2 /*return*/]; // Exit early if no user is authenticated
                }
                return [4 /*yield*/, supabase_1.supabase
                        .from('polls')
                        .select('id')
                        .eq('user_id', user.id) // Only check user's own polls
                        .limit(1)];
            case 2:
                _b = _g.sent(), existingPolls = _b.data, error = _b.error;
                if (error) {
                    console.error("Error checking for existing polls:", error);
                    // Check if this is an RLS error
                    if (error.message.includes('permission denied') || error.message.includes('RLS')) {
                        console.error("RLS policy preventing sample data initialization");
                    }
                    return [2 /*return*/];
                }
                if (!(!existingPolls || existingPolls.length === 0)) return [3 /*break*/, 13];
                console.log("No polls found for user, initializing with sample data");
                samplePolls_2 = [
                    {
                        id: generateId(),
                        title: "Customer Satisfaction Survey",
                        description: "Help us improve our products and services by sharing your feedback.",
                        createdAt: new Date("2025-05-01").toISOString(),
                        updatedAt: new Date("2025-05-01").toISOString(),
                        expiresAt: new Date("2025-06-01").toISOString(),
                        userId: user.id,
                        status: "active",
                        responses: 124,
                        views: 195,
                        is_public: true,
                        questions: [
                            {
                                id: "q1",
                                text: "How satisfied are you with our product?",
                                type: "single",
                                options: [
                                    { id: "o1", text: "Very Dissatisfied", value: "very_dissatisfied" },
                                    { id: "o2", text: "Dissatisfied", value: "dissatisfied" },
                                    { id: "o3", text: "Neutral", value: "neutral" },
                                    { id: "o4", text: "Satisfied", value: "satisfied" },
                                    { id: "o5", text: "Very Satisfied", value: "very_satisfied" }
                                ],
                                required: true,
                                order: 1
                            },
                            {
                                id: "q2",
                                text: "Which features do you use most frequently?",
                                type: "multiple",
                                options: [
                                    { id: "o6", text: "Feature A", value: "feature_a" },
                                    { id: "o7", text: "Feature B", value: "feature_b" },
                                    { id: "o8", text: "Feature C", value: "feature_c" },
                                    { id: "o9", text: "Feature D", value: "feature_d" }
                                ],
                                required: true,
                                order: 2
                            },
                            {
                                id: "q3",
                                text: "How likely are you to recommend our product to others?",
                                type: "likert",
                                options: [
                                    { id: "o10", text: "Not at all likely", value: "1" },
                                    { id: "o11", text: "Slightly likely", value: "2" },
                                    { id: "o12", text: "Moderately likely", value: "3" },
                                    { id: "o13", text: "Very likely", value: "4" },
                                    { id: "o14", text: "Extremely likely", value: "5" }
                                ],
                                required: true,
                                order: 3
                            },
                            {
                                id: "q4",
                                text: "What improvements would you like to see in future updates?",
                                type: "open",
                                required: false,
                                order: 4
                            }
                        ]
                    },
                    {
                        id: generateId(),
                        title: "Product Feature Preferences",
                        description: "Help us prioritize upcoming features by sharing your preferences.",
                        createdAt: new Date("2025-04-28").toISOString(),
                        updatedAt: new Date("2025-04-28").toISOString(),
                        expiresAt: new Date("2025-05-28").toISOString(),
                        userId: user.id,
                        status: "active",
                        responses: 86,
                        views: 132,
                        is_public: true,
                        questions: [
                            {
                                id: "q5",
                                text: "Which of the following features would you find most valuable?",
                                type: "multiple",
                                options: [
                                    { id: "o15", text: "Advanced reporting", value: "reporting" },
                                    { id: "o16", text: "Mobile app integration", value: "mobile" },
                                    { id: "o17", text: "Team collaboration tools", value: "collaboration" },
                                    { id: "o18", text: "Custom branding options", value: "branding" }
                                ],
                                required: true,
                                order: 1
                            },
                            {
                                id: "q6",
                                text: "How important is real-time data syncing to your workflow?",
                                type: "single",
                                options: [
                                    { id: "o19", text: "Not important", value: "not_important" },
                                    { id: "o20", text: "Somewhat important", value: "somewhat_important" },
                                    { id: "o21", text: "Very important", value: "very_important" },
                                    { id: "o22", text: "Critical", value: "critical" }
                                ],
                                required: true,
                                order: 2
                            }
                        ]
                    }
                ];
                _i = 0, samplePolls_1 = samplePolls_2;
                _g.label = 3;
            case 3:
                if (!(_i < samplePolls_1.length)) return [3 /*break*/, 6];
                poll = samplePolls_1[_i];
                return [4 /*yield*/, supabase_1.supabase.from('polls').insert(poll)];
            case 4:
                _g.sent();
                _g.label = 5;
            case 5:
                _i++;
                return [3 /*break*/, 3];
            case 6:
                sampleResponses = Array.from({ length: 10 }).map(function (_, index) { return ({
                    id: generateId(),
                    pollId: samplePolls_2[0].id,
                    responses: {
                        q1: "very_satisfied",
                        q2: ["feature_a", "feature_c"],
                        q4: "Love the product, but would like to see better integration with other tools."
                    },
                    submittedAt: new Date(2025, 4, index + 1).toISOString(),
                    respondentInfo: {
                        deviceType: index % 2 === 0 ? "desktop" : "mobile",
                        browser: index % 3 === 0 ? "Chrome" : index % 3 === 1 ? "Firefox" : "Safari",
                        os: index % 2 === 0 ? "Windows" : "macOS",
                        region: index % 4 === 0 ? "North America" : index % 4 === 1 ? "Europe" : index % 4 === 2 ? "Asia" : "South America"
                    }
                }); });
                _c = 0, sampleResponses_1 = sampleResponses;
                _g.label = 7;
            case 7:
                if (!(_c < sampleResponses_1.length)) return [3 /*break*/, 13];
                response = sampleResponses_1[_c];
                return [4 /*yield*/, supabase_1.supabase
                        .from('responses')
                        .insert({
                        id: response.id,
                        created_at: response.submittedAt,
                        poll_id: response.pollId,
                        user_id: null, // Anonymous responses
                        respondent_info: response.respondentInfo
                    })
                        .select()];
            case 8:
                responseError = (_g.sent()).error;
                if (responseError) {
                    console.error("Error inserting sample response:", responseError);
                    return [3 /*break*/, 12];
                }
                _d = 0, _e = Object.entries(response.responses);
                _g.label = 9;
            case 9:
                if (!(_d < _e.length)) return [3 /*break*/, 12];
                _f = _e[_d], questionId = _f[0], answerValue = _f[1];
                return [4 /*yield*/, supabase_1.supabase
                        .from('answers')
                        .insert({
                        id: generateId(),
                        created_at: response.submittedAt,
                        response_id: response.id,
                        question_id: questionId,
                        answer_value: typeof answerValue === 'string' ? answerValue : JSON.stringify(answerValue)
                    })];
            case 10:
                _g.sent();
                _g.label = 11;
            case 11:
                _d++;
                return [3 /*break*/, 9];
            case 12:
                _c++;
                return [3 /*break*/, 7];
            case 13: return [3 /*break*/, 15];
            case 14:
                error_10 = _g.sent();
                console.error("Error initializing sample data:", error_10);
                return [3 /*break*/, 15];
            case 15: return [2 /*return*/];
        }
    });
}); };
exports.initializeWithSampleData = initializeWithSampleData;
