// error-handler.ts
// Centralized error handling utilities for consistent messaging
import { toast } from "sonner";
import { authRateTracker } from "./auth-rate-tracker";

/**
 * Types of errors for categorization
 */
export enum ErrorType {
  Network = 'network',
  Auth = 'auth',
  RateLimit = 'rateLimit',
  Timeout = 'timeout',
  Permission = 'permission',
  NotFound = 'notFound',
  Server = 'server',
  Database = 'database',  // Added database error type
  Unknown = 'unknown'
}

/**
 * Error handler configuration options
 */
interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  redirectOnAuthFailure?: boolean;
  redirectPath?: string;
  redirectDelay?: number;
}

/**
 * Default error handler options
 */
const defaultOptions: ErrorHandlerOptions = {
  showToast: true,
  logToConsole: true,
  redirectOnAuthFailure: false,
  redirectPath: '/login',
  redirectDelay: 2000
};

/**
 * Categorizes an error by type for consistent handling
 * @param error The error to categorize
 * @returns The error type enum value
 */
export function categorizeError(error: unknown): ErrorType {
  const err = error as Error;
  const message = err.message?.toLowerCase() || '';

  if (message.includes('network') || message.includes('offline') || message.includes('internet')) {
    return ErrorType.Network;
  }

  if (message.includes('rate limit') || message.includes('429') || message.includes('too many requests')) {
    return ErrorType.RateLimit;
  }

  if (message.includes('auth') || message.includes('session') || message.includes('jwt') ||
      message.includes('token') || message.includes('unauthorized') || message.includes('unauthenticated')) {
    return ErrorType.Auth;
  }

  if (message.includes('timeout') || message.includes('timed out')) {
    return ErrorType.Timeout;
  }

  if (message.includes('permission') || message.includes('forbidden') ||
      message.includes('access denied')) {
    return ErrorType.Permission;
  }

  if (message.includes('database') || message.includes('sql') || message.includes('rls') ||
      message.includes('query') || message.includes('supabase')) {
    return ErrorType.Database;
  }

  if (message.includes('not found') || message.includes('404')) {
    return ErrorType.NotFound;
  }

  if (message.includes('server error') || message.includes('500')) {
    return ErrorType.Server;
  }

  return ErrorType.Unknown;
}

/**
 * Gets a user-friendly error message based on the error type
 * @param error The error or error type
 * @returns A user-friendly error message
 */
export function getFriendlyErrorMessage(error: unknown): string {
  const errorType = typeof error === 'string' ? error as ErrorType : categorizeError(error);

  switch (errorType) {
    case ErrorType.Network:
      return "Network connection issue. Please check your internet connection and try again.";

    case ErrorType.Auth:
      return "Your session has expired. Please log in again.";

    case ErrorType.RateLimit:
      const refreshLimit = authRateTracker.checkRefreshRateLimit();
      if (refreshLimit.isApproaching) {
        return `Rate limit approaching (${refreshLimit.percentUsed.toFixed(0)}%). Please wait a moment before making more requests.`;
      }
      return "You've reached the rate limit. Please wait a moment before trying again.";

    case ErrorType.Timeout:
      return "The operation timed out. The server might be experiencing high load or your connection is slow.";

    case ErrorType.Permission:
      return "You don't have permission to perform this action. Please check your access rights.";

    case ErrorType.NotFound:
      return "The requested resource was not found. It may have been moved or deleted.";

    case ErrorType.Server:
      return "The server encountered an error. Please try again later.";

    case ErrorType.Unknown:
    default:
      return "An unexpected error occurred. Please try again later.";
  }
}

/**
 * Handle an error with consistent messaging and actions
 * @param error The error to handle
 * @param options Configuration options for how to handle the error
 */
export function handleError(error: unknown, options?: ErrorHandlerOptions): void {
  const opts = { ...defaultOptions, ...options };
  const errorType = categorizeError(error);
  const message = getFriendlyErrorMessage(error);

  // Log to console if enabled
  if (opts.logToConsole) {
    console.error(`Error (${errorType}):`, error);
  }

  // Show toast notification if enabled
  if (opts.showToast) {
    toast.error(message);
  }

  // Redirect on auth failure if enabled
  if (opts.redirectOnAuthFailure && errorType === ErrorType.Auth) {
    setTimeout(() => {
      window.location.href = opts.redirectPath || '/login';
    }, opts.redirectDelay);
  }
}
