// Helper utilities for auth refresh and session management
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { authRateTracker } from "./auth-rate-tracker";

/**
 * Refresh the authentication session with improved error handling and timeout management
 * With exponential backoff for rate limit handling
 * @param timeoutMs Timeout in milliseconds (default 20000 - 20 seconds)
 * @param retryAttempts Number of retry attempts (default 3)
 * @returns Object containing refresh success status and any error
 */
export async function refreshAuthSession(
  timeoutMs = 30000, // Increased from 20s to 30s
  retryAttempts = 3
): Promise<{success: boolean; error?: Error | null}> {
  // Check if we have a session in localStorage first
  try {
    const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
    const storedSession = localStorage.getItem(authKey);
    console.log(`Auth refresh: Found stored session: ${!!storedSession}`);
  } catch (e) {
    console.warn('Error checking localStorage for session:', e);
  }
  let lastError: Error | null = null;

  // Try multiple times with increasing delays (exponential backoff)
  for (let attempt = 1; attempt <= retryAttempts; attempt++) {
    try {
      console.log(`Attempting to refresh authentication session... (Attempt ${attempt}/${retryAttempts})`);

      // If not the first attempt, add delay with exponential backoff
      // This helps with rate limiting (Supabase limits to 150 refreshes per 5 minutes per IP)
      if (attempt > 1) {
        const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 second delay
        console.log(`Waiting ${delayMs}ms before retry attempt ${attempt}...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      // Use a more reliable approach with a simple timeout
      try {
        // Set a timeout for the refresh operation
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort(`Authentication refresh timed out after ${timeoutMs/1000}s`);
        }, timeoutMs);

        try {
          // Attempt to refresh the session
          const { data, error } = await supabase.auth.refreshSession();

          // Clear the timeout since we got a response
          clearTimeout(timeoutId);

          // Handle any errors from the refresh
          if (error) {
            console.warn(`Auth refresh error on attempt ${attempt}:`, error);
            lastError = error;

            // Check if it looks like a rate limit error
            if (error.message?.includes('rate limit') ||
                error.message?.includes('429') ||
                error.message?.includes('too many requests')) {
              console.log('Rate limit detected, will retry with backoff');
              continue; // Try again with backoff
            } else {
              return { success: false, error }; // Other error, don't retry
            }
          }

          // Check if we got a valid session back
          if (!data.session) {
            console.warn("Auth refresh returned no session");
            lastError = new Error("No session returned from refresh");
            continue; // Try again
          }

          // Success! We got a valid session
          console.log("Authentication session refreshed successfully");
          return { success: true };
        } catch (error) {
          // Clear the timeout to prevent memory leaks
          clearTimeout(timeoutId);
          throw error; // Re-throw to be caught by the outer catch
        }
      } catch (error) {
        // This catches both timeout errors and other exceptions
        console.error(`Auth refresh exception on attempt ${attempt}:`, error);
        lastError = error as Error;

        // If this was a timeout, add more context
        if (error instanceof DOMException && error.name === 'AbortError') {
          lastError = new Error(`Authentication refresh timed out after ${timeoutMs/1000}s`);
        }

        // Only continue retrying if we haven't hit the maximum attempts
        if (attempt < retryAttempts) continue;
      }
    } catch (error) {
      // This catches any other unexpected errors in the retry loop
      console.error(`Unexpected error during auth refresh (attempt ${attempt}):`, error);
      lastError = error as Error;

      // Only continue retrying if we haven't hit the maximum attempts
      if (attempt < retryAttempts) continue;
    }
  }

  // If we get here, all attempts failed
  return { success: false, error: lastError };
}

/**
 * Check if authentication session is valid
 * @returns Promise resolving to boolean indicating if session is valid
 */
export async function isSessionValid(): Promise<boolean> {
  try {
    // Record this verification attempt
    authRateTracker.recordVerify();

    // Check verify rate limits
    const verifyLimit = authRateTracker.checkVerifyRateLimit();
    if (verifyLimit.isApproaching) {
      console.warn(`Rate limit warning: ${verifyLimit.percentUsed.toFixed(1)}% of verification limit used`);
    }

    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.warn("Session validation error:", error);
      return false;
    }

    // Check if session exists and is not expired
    if (data.session) {
      const now = Math.floor(Date.now() / 1000);
      if (data.session.expires_at && data.session.expires_at < now) {
        console.log("Session is expired");
        return false;
      }
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking session validity:", error);
    return false;
  }
}

/**
 * Clear corrupted session data from localStorage and cookies
 */
export async function clearCorruptedSession(): Promise<void> {
  try {
    console.log("Clearing potentially corrupted session data...");

    // Sign out to clear Supabase session
    await supabase.auth.signOut();

    // Clear specific localStorage items that might be corrupted
    const keysToRemove = [
      'sb-sumruaeyfidjlssrmfrm-auth-token',
      'pollgpt_polls_cache',
      'pollgpt_retry_after_refresh'
    ];

    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        console.warn(`Failed to remove ${key} from localStorage:`, e);
      }
    });

    console.log("Session cleanup completed");
  } catch (error) {
    console.error("Error during session cleanup:", error);
  }
}

/**
 * Handles a session timeout, shows appropriate messages and redirects if needed
 * @param forceRefresh Force a refresh even if minimum interval hasn't passed
 */
export async function handleSessionTimeout(forceRefresh = false): Promise<boolean> {
  // Check for rate limit status
  const refreshLimit = authRateTracker.checkRefreshRateLimit();

  // If we're approaching the rate limit, provide a warning
  if (refreshLimit.isApproaching) {
    console.warn(`Rate limit warning: ${refreshLimit.percentUsed.toFixed(1)}% of refresh limit used`);
  }

  // Check if we've refreshed within the rate limit window
  if (!forceRefresh && !authRateTracker.canRefreshToken()) {
    const waitTime = authRateTracker.getTimeUntilNextAllowedRefresh();
    console.log(`Skipping refresh - need to wait ${waitTime}ms before next refresh`);
    toast.info("Session recently refreshed, please try your action again");
    return true; // Return true to allow operation to continue
  }

  toast.loading("Refreshing your session...");

  // Record that we're attempting a refresh
  authRateTracker.recordRefresh(false); // Will update to success if it succeeds

  const { success, error } = await refreshAuthSession();

  if (success) {
    // Record successful refresh
    authRateTracker.recordRefresh(true);
    toast.success("Session refreshed successfully");
    return true;
  } else {
    if (error?.message?.includes('rate limit') || error?.message?.includes('429')) {
      toast.error("Too many session refreshes. Please wait a moment before trying again.");
      console.warn("Rate limit hit during session refresh:", error);
      return false;
    } else {
      toast.error("Your session has expired. Please log in again.");
      // Error details for debugging
      if (error) {
        console.error("Session refresh failed:", error);
      }

      // Redirect to login after short delay
      setTimeout(() => {
        window.location.href = "/login";
      }, 2000);

      return false;
    }
  }
}
