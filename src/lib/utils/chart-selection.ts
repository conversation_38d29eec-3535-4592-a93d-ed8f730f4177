import { ChartDataPoint, ProcessedQuestion } from '@/lib/types/poll';

// Define chart types
export type ChartType = 'pie' | 'bar' | 'horizontalBar' | 'donut' | 'radar' | 'treemap' | 'wordCloud';

interface ChartSelectionCriteria {
  questionType: 'single' | 'multiple' | 'likert' | 'open';
  optionCount: number;
  responseCount: number;
  distributionScore: number; // Measure of how evenly distributed the responses are
  hasTimeComponent: boolean;
}

/**
 * Determines the best chart type for visualizing a question based on its properties
 */
export function selectBestChartType(question: ProcessedQuestion, totalResponses: number): ChartType {
  const results = question.results as ChartDataPoint[];

  // For open-ended questions, suggest a word cloud
  if (question.type === 'open') {
    return 'wordCloud';
  }

  // Calculate distribution score - how evenly distributed are the responses?
  // (1 = perfectly even, 0 = all responses for one option)
  let distributionScore = 1;
  if (results.length > 1) {
    const totalValues = results.reduce((sum, item) => sum + item.value, 0);
    if (totalValues > 0) {
      const expectedPerOption = totalValues / results.length;
      const deviations = results.map(r => Math.abs(r.value - expectedPerOption));
      const avgDeviation = deviations.reduce((sum, dev) => sum + dev, 0) / results.length;
      distributionScore = 1 - (avgDeviation / expectedPerOption);
    }
  }

  // Calculate criteria
  const criteria: ChartSelectionCriteria = {
    questionType: question.type as 'single' | 'multiple' | 'likert' | 'open',
    optionCount: results.length,
    responseCount: totalResponses,
    distributionScore,
    hasTimeComponent: question.text ? (
      question.text.toLowerCase().includes('time') ||
      question.text.toLowerCase().includes('when') ||
      question.text.toLowerCase().includes('frequency')
    ) : false
  };

  // Logic for selecting chart type
  if (question.type === 'likert' && question.text && question.text.toLowerCase().includes('recommend')) {
    // For NPS questions, always use horizontal bar chart for clarity
    return 'horizontalBar';
  }

  // For small number of options with reasonably even distribution
  if (criteria.optionCount <= 5 && criteria.distributionScore > 0.3) {
    // Pie charts are good for showing proportions of a whole
    return 'pie';
  }

  // For small to medium number of options with less even distribution
  if (criteria.optionCount <= 7 && criteria.distributionScore <= 0.3) {
    // Donut charts can handle slightly more uneven distribution while showing proportions
    return 'donut';
  }

  // For medium number of options (good comparison between items)
  if (criteria.optionCount <= 10) {
    // Horizontal bar charts are better when you have longer option labels
    return 'horizontalBar';
  }

  // For multiple-choice questions with many options
  if (question.type === 'multiple' && criteria.optionCount > 10) {
    // Treemaps can handle larger sets of data while showing hierarchical relationships
    return 'treemap';
  }

  // For questions with categorical data and many options or comparing values across categories
  if (criteria.optionCount > 10) {
    // Vertical bar charts can handle more options
    return 'bar';
  }

  // Default to bar chart as a safe option for most data types
  return 'bar';
}

/**
 * Enhances chart options based on data characteristics
 */
export function enhanceChartOptions(chartType: ChartType, data: ChartDataPoint[], totalResponses: number) {
  switch (chartType) {
    case 'pie':
    case 'donut':
      return {
        showLabels: data.length <= 5,
        showLegend: data.length > 3,
        explode: detectOutliers(data), // Make outliers "pop out" from the pie
        tooltipPercentage: true
      };

    case 'bar':
    case 'horizontalBar':
      return {
        showGridLines: true,
        sortBars: data.length > 5, // Sort bars for easier comparison with many options
        barSize: data.length > 10 ? 20 : 40,
        showPercentages: totalResponses > 0
      };

    case 'radar':
      return {
        filled: true,
        showLegend: false
      };

    default:
      return {};
  }
}

/**
 * Detects outliers in data that should be emphasized
 */
function detectOutliers(data: ChartDataPoint[]): number[] {
  if (data.length <= 1) return [];

  // Simple outlier detection - find values that are significantly higher than others
  const values = data.map(d => d.value);
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const stdDev = Math.sqrt(
    values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  );

  // Find indices of values that are more than 2 standard deviations from the mean
  return values
    .map((val, idx) => (Math.abs(val - mean) > 1.5 * stdDev ? idx : -1))
    .filter(idx => idx !== -1);
}
