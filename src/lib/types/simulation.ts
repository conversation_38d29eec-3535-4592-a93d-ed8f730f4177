// Simulation types for LLM-as-Simulator poll simulation

export interface SimulationRequest {
  pollQuestion: string;
  pollOptions: string[];
  demographic: {
    group: string;        // e.g., "high school students", "working professionals"
    size: number;         // e.g., 30, 100, 500
    context?: string;     // additional demographic context
  };
  responseFormat: 'distribution' | 'individual' | 'both';
}

// Multi-demographic batch simulation types
export interface BatchSimulationRequest {
  pollQuestion: string;
  pollOptions: string[];
  demographics: Array<{
    group: string;
    size: number;
    context?: string;
  }>;
  responseFormat: 'distribution' | 'individual' | 'both';
  batchId?: string;
  pollId?: string; // Optional poll ID for associating with existing polls
}

export interface BatchSimulationProgress {
  batchId: string;
  totalDemographics: number;
  completedDemographics: number;
  currentDemographic?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: Date;
  estimatedCompletionAt?: Date;
  error?: string;
}

export interface BatchSimulationResult {
  batchId: string;
  pollQuestion: string;
  pollOptions: string[];
  results: SimulationResponse[];
  comparison: DemographicComparison;
  metadata: {
    totalDemographics: number;
    totalResponses: number;
    completedAt: Date;
    totalCost: number;
    cachePerformance?: {
      hitRate: number;           // Percentage of demographics that were cached (0-1)
      timeSavedSeconds: number;  // Estimated time saved due to caching
      cachedDemographics?: string[];  // List of demographics that were cached
    };
  };
}

export interface DemographicComparison {
  optionRankings: Record<string, Array<{ demographic: string; percentage: number; rank: number }>>;
  significantDifferences: Array<{
    option: string;
    demographics: string[];
    difference: number;
    significance: 'low' | 'medium' | 'high';
  }>;
  insights: string[];
  consensusOptions: string[];  // Options with high agreement across demographics
  polarizingOptions: string[]; // Options with high variance across demographics
}

export interface SimulationResponse {
  simulationId: string;
  metadata: {
    demographic: string;
    sampleSize: number;
    confidence: number;
    citations: string[];
  };
  results: {
    distribution: Record<string, number>;
    individuals?: SimulatedResponse[];
    analysis: string;
  };
}

export interface SimulatedResponse {
  responseId: string;
  selectedOptions: string[];
  reasoning?: string;
  demographicContext?: string;
}

export interface SimulationCacheEntry {
  questionHash: string;
  demographicKey: string;
  cachedResult: SimulationResponse;
  cacheExpiresAt: Date;
  createdAt: Date;
}

export interface SimulationMetrics {
  totalSimulations: number;
  averageResponseTime: number;
  cacheHitRate: number;
  totalCost: number;
  averageCostPerSimulation: number;
}

// Database types for simulation storage
export interface PollSimulation {
  id: string;
  poll_id: string;
  demographic_group: string;
  sample_size: number;
  simulation_data: SimulationResponse;
  confidence_score: number;
  citations: string[];
  created_at: string;
  created_by: string;
}

export interface SimulationCache {
  id: string;
  question_hash: string;
  demographic_key: string;
  cached_result: SimulationResponse;
  cache_expires_at: string;
  created_at: string;
}

// Prompt template types
export interface PromptTemplate {
  system: string;
  user: string;
  responseFormat: string;
}

export interface SimulationPromptData {
  question: string;
  options: string[];
  demographic: string;
  sampleSize: number;
  context?: string;
}
