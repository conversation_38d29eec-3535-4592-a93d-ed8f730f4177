import { sdk, setupMcpServers } from './index';
// The poll scraper server is registered automatically when imported in setupMcpServers
import { scrapeWithPuppeteer, generatePdf } from '../utils/puppeteer-utils';

/**
 * Example of how to initialize and use MCP servers in your application
 */
export async function initializeMcpServices() {
  // Set up all MCP servers
  setupMcpServers();

  // The poll scraper is already instantiated as a singleton when imported
  // No need to create a new instance

  return sdk;
}

/**
 * Example of how to use the Brave Search server
 */
export async function searchWithBrave(query: string) {
  try {
    // Make sure API key is set in .env file as BRAVE_SEARCH_API_KEY
    const results = await sdk.invoke('brave-search', {
      query,
      count: 5,
    });

    return results;
  } catch (error) {
    console.error('Error using Brave Search:', error);
    return null;
  }
}

/**
 * Example of how to use the Puppeteer MCP server
 */
export async function scrapePollData(url: string) {
  try {
    // Use the custom poll scraper
    const pollData = await sdk.invoke('poll-scraper', {
      url,
      questionSelector: '.poll-question',
      optionsSelector: '.poll-option',
    });

    return pollData;
  } catch (error) {
    console.error('Error scraping poll data:', error);

    // Fallback to basic scraping
    try {
      const content = await scrapeWithPuppeteer(url);
      return { content, source: url };
    } catch (fallbackError) {
      console.error('Fallback scraping failed:', fallbackError);
      return null;
    }
  }
}

/**
 * Example of generating a PDF report from a poll
 */
export async function generatePollReport(pollUrl: string, outputPath: string) {
  try {
    // First scrape the poll data
    const pollData = await scrapePollData(pollUrl);

    if (!pollData) {
      throw new Error('Failed to scrape poll data');
    }

    // Generate a PDF of the poll page
    await generatePdf(pollUrl, outputPath);

    return {
      pollData,
      pdfPath: outputPath,
    };
  } catch (error) {
    console.error('Error generating poll report:', error);
    return null;
  }
}