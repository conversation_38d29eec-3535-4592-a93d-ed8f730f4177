"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";
import { clearCorruptedSession } from "@/lib/utils/auth-refresh";
import { Button } from "@/components/ui/button";

interface AuthGuardProps {
  redirectUrl?: string;
  children: React.ReactNode;
  pollId?: string;
}

export default function AuthGuard({ redirectUrl = '/login', children, pollId }: AuthGuardProps) {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [loadingTime, setLoadingTime] = useState(0);
  const [showCleanupOption, setShowCleanupOption] = useState(false);

  // Track loading time with more efficient approach
  useEffect(() => {
    if (loading) {
      const startTime = Date.now();

      // Update loading time every second
      const updateTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        setLoadingTime(elapsed);
      }, 1000);

      // Show cleanup option after 6 seconds of loading (reduced from 10)
      const cleanupTimer = setTimeout(() => {
        setShowCleanupOption(true);
      }, 6000);

      return () => {
        clearInterval(updateTimer);
        clearTimeout(cleanupTimer);
      };
    } else {
      setLoadingTime(0);
      setShowCleanupOption(false);
    }
  }, [loading]);

  useEffect(() => {
    // Wait for auth state to load
    if (loading) return;

    // Check if user is authenticated
    if (!user) {
      // Prevent infinite redirect loops by checking if we're already on the login page
      const isLoginPage = typeof window !== 'undefined' &&
        window.location.pathname.includes('login');

      if (!isLoginPage) {
        // Build the redirect URL with the return path
        const returnPath = pollId
          ? `/dashboard/polls/${pollId}/simulate`
          : typeof window !== 'undefined' ? window.location.pathname : '';

        const returnUrl = encodeURIComponent(returnPath);
        router.push(`${redirectUrl}?redirect=${returnUrl}`);
      }
    }
  }, [user, loading, router, redirectUrl, pollId]);

  const handleSessionCleanup = async () => {
    try {
      await clearCorruptedSession();
      // Redirect to login after cleanup
      window.location.href = '/login';
    } catch (error) {
      console.error('Error during session cleanup:', error);
      // Still redirect to login
      window.location.href = '/login';
    }
  };

  if (loading) {
    return (
      <div className="container max-w-7xl py-6 space-y-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="bg-background border rounded-lg p-8 max-w-md mx-auto shadow-sm">
            <div className="text-center space-y-6">
              {/* Staggered dot loading animation */}
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 bg-primary rounded-full animate-pulse"></div>
                <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              </div>

              <div className="space-y-3">
                <p className="text-muted-foreground">
                  Verifying authentication...
                  {loadingTime > 3 && ` (${loadingTime}s)`}
                </p>

                {loadingTime > 4 && (
                  <p className="text-sm text-muted-foreground">
                    This is taking longer than usual. Please wait...
                  </p>
                )}

                {showCleanupOption && (
                  <div className="space-y-3 pt-2">
                    <p className="text-sm text-muted-foreground">
                      Still having trouble? This might be due to corrupted session data.
                    </p>
                    <Button
                      onClick={handleSessionCleanup}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      Clear Session & Retry
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container max-w-7xl py-6 space-y-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-gray-600">Redirecting to login...</p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
