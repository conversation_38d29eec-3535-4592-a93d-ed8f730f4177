import React from 'react';
import { formatActivityTime, ActivityItem } from '@/lib/services/activity';
import { CheckCircle, FileText, MessageCircle, ThumbsUp } from 'lucide-react';

interface ActivityItemProps {
  activity: ActivityItem;
  isLast?: boolean;
}

const ActivityIcon = ({ type }: { type: ActivityItem['type'] }) => {
  const iconProps = {
    size: 16,
  };

  switch (type) {
    case 'poll_created':
      return <FileText {...iconProps} className="text-blue-600 dark:text-blue-400" />;
    case 'response_received':
      return <MessageCircle {...iconProps} className="text-green-600 dark:text-green-400" />;
    case 'poll_completed':
      return <CheckCircle {...iconProps} className="text-green-600 dark:text-green-400" />;
    case 'milestone_reached':
      return <ThumbsUp {...iconProps} className="text-amber-600 dark:text-amber-400" />;
    default:
      return <FileText {...iconProps} className="text-blue-600 dark:text-blue-400" />;
  }
};

const getColorClasses = (type: ActivityItem['type']) => {
  switch (type) {
    case 'poll_created':
      return {
        dotBg: 'bg-blue-100 dark:bg-blue-900/30',
        dotColor: 'bg-blue-500',
        iconBg: 'bg-blue-100 dark:bg-blue-900/30',
        iconColor: 'text-blue-600 dark:text-blue-400',
      };
    case 'response_received':
      return {
        dotBg: 'bg-green-100 dark:bg-green-900/30',
        dotColor: 'bg-green-500',
        iconBg: 'bg-green-100 dark:bg-green-900/30',
        iconColor: 'text-green-600 dark:text-green-400',
      };
    case 'poll_completed':
      return {
        dotBg: 'bg-green-100 dark:bg-green-900/30',
        dotColor: 'bg-green-500',
        iconBg: 'bg-green-100 dark:bg-green-900/30',
        iconColor: 'text-green-600 dark:text-green-400',
      };
    case 'milestone_reached':
      return {
        dotBg: 'bg-amber-100 dark:bg-amber-900/30',
        dotColor: 'bg-amber-500',
        iconBg: 'bg-amber-100 dark:bg-amber-900/30',
        iconColor: 'text-amber-600 dark:text-amber-400',
      };
    default:
      return {
        dotBg: 'bg-blue-100 dark:bg-blue-900/30',
        dotColor: 'bg-blue-500',
        iconBg: 'bg-blue-100 dark:bg-blue-900/30',
        iconColor: 'text-blue-600 dark:text-blue-400',
      };
  }
};

export function ActivityItemComponent({ activity, isLast = false }: ActivityItemProps) {
  const colorClasses = getColorClasses(activity.type);

  return (
    <div className="relative pl-6 pb-6 group">
      {/* Timeline connector */}
      {!isLast && (
        <div className="absolute top-0 left-2.5 h-full w-px bg-muted"></div>
      )}

      <div className="flex items-start gap-4 p-4 rounded-lg hover:bg-muted/40 transition-colors relative">
        {/* Timeline dot */}
        <div className={`absolute -left-6 top-5 rounded-full w-5 h-5 ${colorClasses.dotBg} flex items-center justify-center border-4 border-background`}>
          <div className={`w-2.5 h-2.5 rounded-full ${colorClasses.dotColor}`}></div>
        </div>

        {/* Activity icon */}
        <div className={`rounded-full w-10 h-10 ${colorClasses.iconBg} flex items-center justify-center ${colorClasses.iconColor} shrink-0`}>
          <ActivityIcon type={activity.type} />
        </div>

        {/* Content */}
        <div className="flex-1">
          <p className="text-sm font-medium">{activity.title}</p>
          <p className="text-xs text-muted-foreground mt-1">{activity.description}</p>
        </div>

        {/* Timestamp */}
        <div className="flex flex-col items-end">
          <span className="text-xs font-medium text-muted-foreground px-2 py-1 rounded-full bg-muted">
            {formatActivityTime(activity.timestamp)}
          </span>
        </div>
      </div>
    </div>
  );
}
