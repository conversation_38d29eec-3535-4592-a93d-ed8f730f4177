'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

// This component is only for development to help debug authentication issues
export function AuthDebugger() {
  const { user, session, loading } = useAuth()
  const [cookies, setCookies] = useState<string[]>([])

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') return

    // Get all cookies and parse them
    const allCookies = document.cookie.split(';').map(cookie => cookie.trim())
    setCookies(allCookies)

    // Log auth state for debugging
    console.log('AuthDebugger: Auth state', {
      user: user ? { id: user.id, email: user.email } : null,
      sessionExists: !!session,
      loading,
      cookies: allCookies
    })
  }, [user, session, loading])

  // Don't render anything in production
  if (process.env.NODE_ENV !== 'development') return null

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: 'rgba(0,0,0,0.8)',
        padding: '10px',
        borderRadius: '5px',
        color: 'white',
        zIndex: 9999,
        fontSize: '12px',
        width: '320px',
        maxHeight: '300px',
        overflowY: 'auto'
      }}
    >
      <button
        style={{
          position: 'absolute',
          right: '5px',
          top: '5px',
          backgroundColor: 'transparent',
          color: 'white',
          border: 'none',
          cursor: 'pointer'
        }}
        onClick={() => {
          const debug = document.getElementById('auth-debug-content')
          if (debug) debug.style.display = debug.style.display === 'none' ? 'block' : 'none'
        }}
      >
        Toggle
      </button>
      <div id="auth-debug-content">
        <h4>Auth Debug Info</h4>
        <div>User: {user ? `${user.email} (${user.id.substring(0, 8)}...)` : 'Not logged in'}</div>
        <div>Session: {session ? 'Active' : 'None'}</div>
        <div>Loading: {loading ? 'Yes' : 'No'}</div>
        <h5>Cookies:</h5>
        <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
          {cookies.map((cookie, i) => <li key={i}>{cookie}</li>)}
        </ul>
      </div>
    </div>
  )
}
