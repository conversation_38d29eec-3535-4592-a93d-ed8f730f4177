'use client';

import { useState, Suspense } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/components/providers/auth-provider';

// Inner component that uses the search params
function WelcomeModalContent() {
  const { completeOnboarding } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const showOnboarding = searchParams?.get('onboarding') === 'true';
  const [open, setOpen] = useState(showOnboarding);
  const [step, setStep] = useState(1);
  const totalSteps = 3;

  const handleClose = () => {
    setOpen(false);
    completeOnboarding();

    // Remove the onboarding parameter from the URL
    const url = new URL(window.location.href);
    url.searchParams.delete('onboarding');
    window.history.replaceState({}, '', url);
  };

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    } else {
      handleClose();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            {step === 1 && "Welcome to PollGPT! 🎉"}
            {step === 2 && "Create AI-Powered Polls"}
            {step === 3 && "Ready to Get Started"}
          </DialogTitle>
          <DialogDescription className="pt-2">
            {step === 1 && "We're excited to have you join us! Let's take a quick tour of what you can do with PollGPT."}
            {step === 2 && "PollGPT uses AI to help you create engaging polls in seconds. Just provide some content, and we'll generate intelligent questions."}
            {step === 3 && "You're all set! Start by creating your first poll or explore the dashboard to see what's possible."}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {step === 1 && (
            <div className="space-y-4">
              <div className="bg-primary/10 p-4 rounded-lg">
                <h3 className="font-medium text-primary">Create Polls Effortlessly</h3>
                <p className="text-sm mt-1">Generate intelligent questions from any content with our AI assistant.</p>
              </div>
              <div className="bg-primary/10 p-4 rounded-lg">
                <h3 className="font-medium text-primary">Share with Anyone</h3>
                <p className="text-sm mt-1">Distribute your polls via custom links or embed them on your website.</p>
              </div>
              <div className="bg-primary/10 p-4 rounded-lg">
                <h3 className="font-medium text-primary">Analyze Results</h3>
                <p className="text-sm mt-1">Get AI-powered insights from your poll responses.</p>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium">How It Works</h3>
                <ol className="mt-2 space-y-2 text-sm pl-5 list-decimal">
                  <li>Paste any content or start from scratch</li>
                  <li>Our AI generates relevant questions</li>
                  <li>Customize your poll as needed</li>
                  <li>Share it with your audience</li>
                </ol>
              </div>
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium">Poll Types</h3>
                <p className="text-sm mt-1">Create multiple choice, rating scales, open-ended questions, and more.</p>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium">Quick Tips</h3>
                <ul className="mt-2 space-y-2 text-sm pl-5 list-disc">
                  <li>Use the dashboard to manage all your polls</li>
                  <li>Check the results page for response analytics</li>
                  <li>Customize your polls to match your brand</li>
                </ul>
              </div>
              <Button
                className="w-full"
                onClick={() => {
                  handleClose();
                  router.push('/dashboard/create/conversational');
                }}
              >
                Create Your First Poll
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalSteps }).map((_, i) => (
              <div
                key={i}
                className={`h-2 w-2 rounded-full ${
                  i + 1 === step ? 'bg-primary' : 'bg-muted'
                }`}
              />
            ))}
          </div>
          <DialogFooter className="sm:justify-end">
            {step > 1 && (
              <Button variant="outline" onClick={prevStep}>
                Back
              </Button>
            )}
            <Button onClick={nextStep}>
              {step < totalSteps ? 'Next' : 'Get Started'}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Wrapper component with Suspense boundary
export function WelcomeModal() {
  return (
    <Suspense fallback={null}>
      <WelcomeModalContent />
    </Suspense>
  );
}
