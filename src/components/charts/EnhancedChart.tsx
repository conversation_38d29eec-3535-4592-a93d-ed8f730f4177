"use client";

import React, { useState, useMemo, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>, Bar,
  PieChart, Pie,
  XAxis, YAxis,
  CartesianGrid, Tooltip,
  Legend, Cell,
  ResponsiveContainer,
  RadarChart, PolarGrid,
  PolarAngleAxis, PolarRadiusAxis,
  Radar, Treemap,
  Label
} from 'recharts';
import { Button } from "@/components/ui/button";
import { selectBestChartType, enhanceChartOptions, ChartType } from '@/lib/utils/chart-selection';
import { ChartDataPoint, ProcessedQuestion } from '@/lib/types/poll';
import { getThemeColors, getCategoryColor } from '@/lib/utils/chart-themes';
import { identifyOutliers } from '@/lib/utils/statistics';

interface ChartDisplayProps {
  question: ProcessedQuestion;
  totalResponses: number;
}

// Define a more specific tooltip payload type
type TooltipPayload = {
  name: string;
  value: number;
  [key: string]: unknown;
};

// Use a custom functional component for the tooltip to avoid type issues
const CustomTooltip = ({ active, payload, totalResponses }: {
  active?: boolean;
  payload?: TooltipPayload[];
  totalResponses: number;
}) => {
  if (active && payload && payload.length) {
    const value = payload[0].value as number;
    const percentage = totalResponses > 0
      ? `${((value / totalResponses) * 100).toFixed(1)}%`
      : '0%';

    return (
      <div className="bg-background border border-border p-3 rounded-lg shadow-md">
        <p className="font-medium">{String(payload[0].name)}</p>
        <p className="text-sm">
          {value} responses
          <span className="ml-1 opacity-80">({percentage})</span>
        </p>
      </div>
    );
  }
  return null;
};

interface WordCloudItem {
  text: string;
  value: number;
}

// Use a more general type for responses
interface WordCloudProps {
  responses: unknown[];
}

const WordCloud: React.FC<WordCloudProps> = ({ responses }) => {
  // This is a simplified word cloud implementation
  // In a real implementation, use a library like react-wordcloud
  const wordFrequency = useMemo(() => {
    if (!responses || responses.length === 0) return [];

    // Extract words from all responses
    const words = responses
      .flatMap(response => {
        // Handle different response formats
        const text = typeof response === 'object' && response !== null && 'response' in response &&
          typeof response.response === 'string'
          ? response.response
          : '';
        return text.toLowerCase().split(/\s+/);
      })
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'from', 'have', 'were'].includes(word));

    // Count word frequency
    const frequency: Record<string, number> = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    // Convert to array and sort by frequency
    return Object.entries(frequency)
      .map(([text, value]) => ({ text, value }))
      .sort((a, b) => (b.value as number) - (a.value as number))
      .slice(0, 30);
  }, [responses]);

  if (wordFrequency.length === 0) {
    return <div className="h-72 flex items-center justify-center">
      <p className="text-muted-foreground">Insufficient data for word cloud</p>
    </div>;
  }

  return (
    <div className="h-72 flex items-center justify-center">
      <div className="flex flex-wrap gap-2 justify-center items-center">
        {wordFrequency.map((word: WordCloudItem) => (
          <span
            key={word.text}
            className="inline-block rounded-full px-3 py-1 text-white"
            style={{
              fontSize: `${Math.max(1, Math.min(2.5, 0.8 + (word.value as number) / 5))}rem`,
              backgroundColor: getCategoryColor(word.text),
              opacity: 0.7 + ((word.value as number) / (wordFrequency[0].value as number)) * 0.3
            }}
          >
            {word.text}
          </span>
        ))}
      </div>
    </div>
  );
};

// Create an interface that extends ResultItem for our chart data points
interface EnhancedChartDataPoint extends ChartDataPoint {
  [key: string]: unknown;
}

export const EnhancedChart: React.FC<ChartDisplayProps> = ({ question, totalResponses }) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType | null>(null);
  // Get theme colors - this will update when the theme changes
  const [themeColors, setThemeColors] = useState<string[]>(getThemeColors());

  // Update colors when theme changes
  useEffect(() => {
    const handleThemeChange = () => {
      setThemeColors(getThemeColors());
    };

    window.addEventListener('chartthemechange', handleThemeChange);
    return () => {
      window.removeEventListener('chartthemechange', handleThemeChange);
    };
  }, []);

  // Determine the best chart type based on the question and data characteristics
  const recommendedChartType = useMemo(() =>
    selectBestChartType(question, totalResponses),
    [question, totalResponses]
  );

  // Use the recommended chart type if no selection has been made
  const chartType = selectedChartType || recommendedChartType;

  // Get enhanced options for the selected chart type
  const chartOptions = useMemo(() => {
    // Get base options
    const options = enhanceChartOptions(chartType, question.results as ChartDataPoint[], totalResponses);

    // Use statistical analysis to detect outliers for highlighting
    if (question.type !== 'open') {
      const enhancedResults = question.results as EnhancedChartDataPoint[];
      const outlierIndices = identifyOutliers(enhancedResults);
      if (outlierIndices.length > 0) {
        options.explode = outlierIndices;
      }
    }

    return options;
  }, [chartType, question.results, totalResponses, question.type]);

  // For open-ended questions, return a word cloud or text list
  if (question.type === 'open') {
    return <WordCloud responses={question.results} />;
  }

  // Create a tooltip renderer function with a more specific type
  const tooltipRenderer = (props: {
    active?: boolean;
    payload?: TooltipPayload[];
    label?: string;
  }) => (
    <CustomTooltip
      active={props.active}
      payload={props.payload}
      totalResponses={totalResponses}
    />
  );

  const renderChart = () => {
    const data = question.results as ChartDataPoint[];

    switch (chartType) {
      case 'pie':
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={chartOptions.showLabels}
              outerRadius={100}
              label={chartOptions.showLabels ?
                ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%` :
                false
              }
              dataKey="value"
              stroke="rgba(0, 0, 0, 0.1)"
              strokeWidth={2}
              animationDuration={800}
            >
              {data.map((entry, idx) => (
                <Cell
                  key={`cell-${idx}`}
                  fill={getCategoryColor(entry.name)}
                  // Add "pop out" effect for outliers
                  {...(chartOptions.explode?.includes(idx) ? {
                    outerRadius: 110,
                    strokeWidth: 3
                  } : {})}
                />
              ))}
            </Pie>
            <Tooltip content={tooltipRenderer} />
            {chartOptions.showLegend && (
              <Legend
                formatter={(value) => <span style={{color: 'var(--foreground)', fontSize: '12px'}}>{value}</span>}
                iconSize={10}
                wrapperStyle={{paddingTop: '20px'}}
                layout="horizontal"
                verticalAlign="bottom"
                align="center"
              />
            )}
          </PieChart>
        );

      case 'donut':
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              innerRadius={60}
              dataKey="value"
              stroke="rgba(0, 0, 0, 0.1)"
              strokeWidth={2}
              animationDuration={800}
            >
              {data.map((entry, idx) => (
                <Cell
                  key={`cell-${idx}`}
                  fill={getCategoryColor(entry.name)}
                  // Add "pop out" effect for outliers
                  {...(chartOptions.explode?.includes(idx) ? {
                    outerRadius: 110,
                    strokeWidth: 3
                  } : {})}
                />
              ))}
              {question.npsScore !== undefined && (
                <Label
                  value={`NPS: ${question.npsScore}`}
                  position="center"
                  fill="var(--foreground)"
                  style={{ fontSize: '24px', fontWeight: 'bold' }}
                />
              )}
            </Pie>
            <Tooltip content={tooltipRenderer} />
            <Legend
              formatter={(value) => <span style={{color: 'var(--foreground)', fontSize: '12px'}}>{value}</span>}
              iconSize={10}
              wrapperStyle={{paddingTop: '20px'}}
              layout="horizontal"
              verticalAlign="bottom"
              align="center"
            />
          </PieChart>
        );

      case 'horizontalBar':
        // Sort data if needed
        const sortedData = chartOptions.sortBars
          ? [...data].sort((a, b) => b.value - a.value)
          : data;

        return (
          <BarChart
            data={sortedData}
            layout="vertical"
            margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
          >
            {chartOptions.showGridLines && (
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(158, 158, 158, 0.2)" />
            )}
            <XAxis
              type="number"
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              stroke="rgba(158, 158, 158, 0.2)"
            />
            <YAxis
              type="category"
              dataKey="name"
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              width={90}
              stroke="rgba(158, 158, 158, 0.2)"
            />
            <Tooltip content={tooltipRenderer} />
            <Bar
              dataKey="value"
              radius={[0, 4, 4, 0]}
              animationDuration={800}
              barSize={chartOptions.barSize}
            >
              {sortedData.map((entry, idx) => (
                <Cell key={`cell-${idx}`} fill={getCategoryColor(entry.name)} />
              ))}
            </Bar>
          </BarChart>
        );

      case 'bar':
        return (
          <BarChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: data.length > 5 ? 80 : 30 }}
          >
            {chartOptions.showGridLines && (
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(158, 158, 158, 0.2)" />
            )}
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              stroke="rgba(158, 158, 158, 0.2)"
              angle={data.length > 5 ? -45 : 0}
              textAnchor={data.length > 5 ? "end" : "middle"}
              height={data.length > 5 ? 80 : 30}
              interval={0}
            />
            <YAxis
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              stroke="rgba(158, 158, 158, 0.2)"
            />
            <Tooltip content={tooltipRenderer} />
            <Bar
              dataKey="value"
              radius={[4, 4, 0, 0]}
              animationDuration={800}
              barSize={chartOptions.barSize}
            >
              {data.map((entry, idx) => (
                <Cell key={`cell-${idx}`} fill={getCategoryColor(entry.name)} />
              ))}
            </Bar>
          </BarChart>
        );

      case 'radar':
        return (
          <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
            <PolarGrid stroke="rgba(158, 158, 158, 0.2)" />
            <PolarAngleAxis
              dataKey="name"
              tick={{ fill: "var(--foreground)", fontSize: 12 }}
            />
            <PolarRadiusAxis angle={30} domain={[0, 'auto']} />
            <Radar
              name="Responses"
              dataKey="value"
              stroke={themeColors[0]}
              fill={themeColors[0]}
              fillOpacity={0.6}
            />
            <Tooltip content={tooltipRenderer} />
            {chartOptions.showLegend && <Legend />}
          </RadarChart>
        );

      case 'treemap':
        // Use inline function for Treemap content - TypeScript is satisfied this way
        return (
          <Treemap
            data={data}
            dataKey="value"
            aspectRatio={4/3}
            stroke="#fff"
            fill="#8884d8"
            content={React.createElement(({ x, y, width, height, depth, name }) => (
              <g>
                <rect
                  x={x}
                  y={y}
                  width={width}
                  height={height}
                  style={{
                    fill: getCategoryColor(name || 'default'),
                    stroke: '#fff',
                    strokeWidth: 2 / (depth + 1e-10),
                    strokeOpacity: 1 / (depth + 1e-10),
                  }}
                />
                {depth === 1 && name && (
                  <text
                    x={x + width / 2}
                    y={y + height / 2 + 7}
                    textAnchor="middle"
                    fill="#fff"
                    fontSize={14}
                  >
                    {name}
                  </text>
                )}
              </g>
            ))}
          >
            <Tooltip content={tooltipRenderer} />
          </Treemap>
        );

      default:
        return (
          <div className="flex items-center justify-center h-72">
            <p className="text-muted-foreground">No chart available for this data</p>
          </div>
        );
    }
  };

  // Get the chart data for availability checks
  const chartData = question.results as ChartDataPoint[];

  // Available chart types for this question type
  const availableChartTypes: { type: ChartType; label: string }[] = [
    { type: 'pie', label: 'Pie Chart' },
    { type: 'donut', label: 'Donut Chart' },
    { type: 'bar', label: 'Bar Chart' },
    { type: 'horizontalBar', label: 'Horizontal Bar' },
    ...(question.type === 'likert' || chartData.length <= 10 ? [{ type: 'radar' as ChartType, label: 'Radar Chart' }] : []),
    ...(chartData.length > 8 ? [{ type: 'treemap' as ChartType, label: 'Treemap' }] : []),
  ];

  return (
    <div className="space-y-4">
      <div className="h-72">
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </div>

      {/* Chart selector */}
      <div className="flex flex-wrap gap-2 justify-center">
        {availableChartTypes.map(({ type, label }) => (
          <Button
            key={type}
            variant={chartType === type ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedChartType(type)}
            className="text-xs"
          >
            {label}
            {chartType === type && recommendedChartType === type && (
              <span className="ml-1 text-xs opacity-70">(Recommended)</span>
            )}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default EnhancedChart;
