import { SimulationResponse } from "@/lib/types/simulation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, Users, TrendingUp } from "lucide-react";
import { useState } from 'react';

interface SimulationResultsProps {
  results: SimulationResponse;
  className?: string;
  options?: string[];
  enhancedUI?: boolean;
}

export function SimulationResults({
  results,
  className,
  options,
  enhancedUI
}: SimulationResultsProps) {
  const [showAnalysis, setShowAnalysis] = useState(true);
  const [showSources, setShowSources] = useState(false);

  // Add safety checks for undefined properties
  if (!results || !results.results) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Error: Simulation results not available</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Unable to display simulation results due to invalid data format.</p>
          <pre className="mt-2 text-xs bg-muted p-2 rounded">
            {JSON.stringify(results, null, 2)}
          </pre>
        </CardContent>
      </Card>
    );
  }

  // Ensure distribution exists and is an object
  const distribution = results.results.distribution || {};
  // Map option keys to actual text if options prop is provided
  const sortedResults = Object.entries(distribution)
    .map(([key, value]) => {
      let displayKey = key;
      if (options && /^option\d+$/i.test(key)) {
        const optionIdx = parseInt(key.replace(/[^\d]/g, ''), 10) - 1;
        if (options[optionIdx]) displayKey = options[optionIdx];
      }
      return [displayKey, value] as [string, number];
    })
    .sort(([, a], [, b]) => b - a);

  // Enhanced UI/UX
  if (enhancedUI) {
    return (
      <div className={`space-y-6 animate-fade-in ${className || ''}`}>
        {/* Sticky Sub-header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-muted pb-2 mb-2 border-b flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <span className="font-semibold text-lg flex items-center gap-2"><Brain className="h-5 w-5 text-primary" /> AI Simulation Results</span>
            <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded font-medium">{results.metadata?.sampleSize || 'N/A'} responses</span>
          </div>
          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
            <span><Users className="inline h-3 w-3 mr-1" />{results.metadata?.demographic || 'N/A'}</span>
            <span className="font-semibold text-green-700 dark:text-green-400">Confidence: {results.metadata?.confidence ? (results.metadata.confidence * 100).toFixed(0) + '%' : 'N/A'}</span>
          </div>
        </div>

        {/* Response Distribution */}
        <div className="space-y-4">
          <h4 className="font-semibold flex items-center gap-2 text-base sticky top-10 bg-white dark:bg-muted z-10"><TrendingUp className="h-4 w-4" /> Response Distribution</h4>
          <div className="space-y-4">
            {sortedResults.length > 0 ? (
              sortedResults.map(([option, percentage]) => (
                <div key={option} className="space-y-2">
                  <div className="flex items-center justify-between text-base font-medium">
                    <span className="truncate">{option}</span>
                    <span className="text-muted-foreground font-semibold">
                      {Number(percentage).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <div
                      className="bg-primary h-3 rounded-full transition-all duration-300"
                      style={{ width: `${Number(percentage)}%` }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">No distribution data available</div>
            )}
          </div>
        </div>

        {/* Collapsible AI Analysis */}
        {results.results.analysis && (
          <div className="pt-2 border-t">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-primary hover:underline mb-2"
              onClick={() => setShowAnalysis((v) => !v)}
              type="button"
            >
              {showAnalysis ? '▼' : '►'} AI Analysis
            </button>
            {showAnalysis && (
              <p className="text-sm text-muted-foreground leading-relaxed animate-fade-in">
                {results.results.analysis}
              </p>
            )}
          </div>
        )}

        {/* Collapsible Sources */}
        {results.metadata?.citations && Array.isArray(results.metadata.citations) && results.metadata.citations.length > 0 && (
          <div className="pt-2 border-t">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-primary hover:underline mb-2"
              onClick={() => setShowSources((v) => !v)}
              type="button"
            >
              {showSources ? '▼' : '►'} Sources
            </button>
            {showSources && (
              <div className="text-xs text-muted-foreground space-y-1 animate-fade-in">
                {results.metadata.citations.slice(0, 3).map((citation, index) => (
                  <div key={index} className="truncate">
                    {index + 1}. {citation}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Simulation Results
          </CardTitle>
          <Badge variant="secondary" className="gap-1">
            <Users className="h-3 w-3" />
            {results.metadata?.sampleSize || 'N/A'} responses
          </Badge>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Demographic: {results.metadata?.demographic || 'N/A'}</span>
          <span>Confidence: {results.metadata?.confidence ?
            (results.metadata.confidence * 100).toFixed(0) + '%' :
            'N/A'}</span>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 max-h-[60vh] overflow-y-auto">
        {/* Response Distribution */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Response Distribution
          </h4>
          <div className="space-y-3">
            {sortedResults.length > 0 ? (
              sortedResults.map(([option, percentage]) => (
                <div key={option} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium truncate">{option}</span>
                    <span className="text-muted-foreground font-medium">
                      {Number(percentage).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Number(percentage)}%` }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">No distribution data available</div>
            )}
          </div>
        </div>

        {/* Analysis */}
        {results.results.analysis && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">AI Analysis</h4>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {results.results.analysis}
            </p>
          </div>
        )}

        {/* Citations */}
        {results.metadata?.citations && Array.isArray(results.metadata.citations) && results.metadata.citations.length > 0 && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">Sources</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              {results.metadata.citations.slice(0, 3).map((citation, index) => (
                <div key={index} className="truncate">
                  {index + 1}. {citation}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
