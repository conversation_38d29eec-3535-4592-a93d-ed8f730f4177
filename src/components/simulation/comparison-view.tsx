"use client";

import React, { useState } from 'react';
import { BatchSimulationResult } from '@/lib/types/simulation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LineChart, Target, Zap, Download, AlertCircle, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ComparisonViewProps {
  batchResult: BatchSimulationResult;
  className?: string;
}

export function ComparisonView({ batchResult, className }: ComparisonViewProps) {
  const [activeTab, setActiveTab] = useState('performance');
  const [selectedOption, setSelectedOption] = useState<string>(batchResult.pollOptions[0]);

  // Extract comparison data for easier access
  const { comparison, results } = batchResult;

  // Calculate additional metrics for enhanced visualization
  const totalResponses = results.reduce((sum, r) => sum + r.metadata.sampleSize, 0);
  const averageConfidence = results.reduce((sum, r) => sum + r.metadata.confidence, 0) / results.length;

  const getSignificanceColor = (significance: 'low' | 'medium' | 'high') => {
    switch (significance) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const exportResults = () => {
    // Create CSV data for export
    const csvData = [];
    csvData.push(['Demographic', ...batchResult.pollOptions]);

    results.forEach(result => {
      const row = [result.metadata.demographic];
      batchResult.pollOptions.forEach(option => {
        row.push(((result.results.distribution[option] || 0) * 100).toFixed(1) + '%');
      });
      csvData.push(row);
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `poll-simulation-comparison-${Date.now()}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Export */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Demographic Comparison</h3>
          <p className="text-sm text-muted-foreground">
            Analyze response patterns across {results.length} demographic groups with {totalResponses} total responses
          </p>
        </div>
        <Button onClick={exportResults} variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
      </div>

      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab} 
        defaultValue="overview" 
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">
            Overview
          </TabsTrigger>
          <TabsTrigger value="detailed">
            Detailed Analysis
          </TabsTrigger>
          <TabsTrigger value="insights">
            Key Insights
          </TabsTrigger>
          <TabsTrigger value="differences">
            Differences
          </TabsTrigger>
          <TabsTrigger value="performance">
            Performance
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Simulation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm text-muted-foreground">Demographics</span>
                  <span className="text-2xl font-bold">{results.length}</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm text-muted-foreground">Total Responses</span>
                  <span className="text-2xl font-bold">{totalResponses}</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm text-muted-foreground">Avg. Confidence</span>
                  <span className="text-2xl font-bold">{(averageConfidence * 100).toFixed(0)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Consensus vs Polarizing Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-600" />
                  Consensus Options
                </CardTitle>
              </CardHeader>
              <CardContent>
                {comparison.consensusOptions.length > 0 ? (
                  <div className="space-y-2">
                    {comparison.consensusOptions.map(option => (
                      <Badge key={option} variant="secondary" className="mr-2">
                        {option}
                      </Badge>
                    ))}
                    <p className="text-xs text-muted-foreground mt-2">
                      These options show strong agreement across demographics
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No strong consensus options identified
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Zap className="h-4 w-4 text-orange-600" />
                  Polarizing Options
                </CardTitle>
              </CardHeader>
              <CardContent>
                {comparison.polarizingOptions.length > 0 ? (
                  <div className="space-y-2">
                    {comparison.polarizingOptions.map(option => (
                      <Badge key={option} variant="outline" className="mr-2">
                        {option}
                      </Badge>
                    ))}
                    <p className="text-xs text-muted-foreground mt-2">
                      These options show significant variation across demographics
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No highly polarizing options identified
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Option Rankings Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Response Distribution by Option</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {batchResult.pollOptions.map(option => {
                  const optionData = comparison.optionRankings[option] || [];
                  const avgPercentage = optionData.reduce((sum, d) => sum + d.percentage, 0) / optionData.length;

                  return (
                    <div key={option} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{option}</span>
                        <span className="text-sm text-muted-foreground">
                          {avgPercentage.toFixed(1)}% avg
                        </span>
                      </div>
                      <Progress value={avgPercentage} className="h-2" />
                      <div className="flex flex-wrap gap-1">
                        {optionData.map(data => (
                          <Badge
                            key={data.demographic}
                            variant="outline"
                            className="text-xs"
                          >
                            {data.demographic}: {data.percentage.toFixed(1)}%
                          </Badge>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Detailed Analysis Tab */}
        <TabsContent value="detailed" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Option-by-Option Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {batchResult.pollOptions.map(option => (
                  <div
                    key={option}
                    className={cn(
                      "p-4 border rounded-lg cursor-pointer transition-colors",
                      selectedOption === option ? "border-primary bg-primary/5" : "border-border"
                    )}
                    onClick={() => setSelectedOption(option)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{option}</h4>
                      <ChevronRight className={cn(
                        "h-4 w-4 transition-transform",
                        selectedOption === option ? "rotate-90" : ""
                      )} />
                    </div>
                    
                    {selectedOption === option && (
                      <div className="mt-4 space-y-4">
                        <div className="space-y-2">
                          <h5 className="text-sm font-medium">Demographic Breakdown</h5>
                          <div className="space-y-3">
                            {comparison.optionRankings[option]?.sort((a, b) => b.percentage - a.percentage).map(data => (
                              <div key={data.demographic} className="space-y-1">
                                <div className="flex items-center justify-between text-sm">
                                  <span>{data.demographic}</span>
                                  <span className="font-medium">{data.percentage.toFixed(1)}%</span>
                                </div>
                                <Progress value={data.percentage} className="h-1.5" />
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div className="pt-3 border-t">
                          <h5 className="text-sm font-medium mb-2">Demographic Insights</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {comparison.significantDifferences
                              .filter(diff => diff.option === option)
                              .map((diff, idx) => (
                                <li key={idx} className={cn(
                                  "p-2 rounded-md border text-xs",
                                  getSignificanceColor(diff.significance)
                                )}>
                                  <span className="font-medium">
                                    {diff.significance === 'high' ? 'Major difference' : 
                                     diff.significance === 'medium' ? 'Notable difference' : 'Slight difference'}:
                                  </span> {diff.demographics.join(' vs ')} 
                                  ({diff.difference.toFixed(1)}% gap)
                                </li>
                              ))}
                            {comparison.significantDifferences.filter(diff => diff.option === option).length === 0 && (
                              <li className="text-xs italic">No significant demographic differences for this option</li>
                            )}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Key Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <LineChart className="h-4 w-4 text-primary" />
                Key Demographic Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {comparison.insights.length > 0 ? (
                  <ul className="space-y-3">
                    {comparison.insights.map((insight, index) => (
                      <li key={index} className="p-3 bg-muted/50 rounded-lg text-sm">
                        {insight}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No significant insights were identified across demographics
                  </p>
                )}
                
                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium mb-2">Demographic Confidence</h4>
                  <div className="space-y-3">
                    {results.map(result => (
                      <div key={result.simulationId} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{result.metadata.demographic}</span>
                          <span className="font-medium">{(result.metadata.confidence * 100).toFixed(0)}% confidence</span>
                        </div>
                        <Progress 
                          value={result.metadata.confidence * 100} 
                          className={cn(
                            "h-1.5",
                            result.metadata.confidence > 0.8 ? "bg-green-100" : 
                            result.metadata.confidence > 0.6 ? "bg-yellow-100" : "bg-red-100"
                          )} 
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Differences Tab */}
        <TabsContent value="differences" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Significant Demographic Differences</CardTitle>
            </CardHeader>
            <CardContent>
              {comparison.significantDifferences.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    {comparison.significantDifferences
                      .sort((a, b) => b.difference - a.difference)
                      .map((diff, idx) => (
                        <div 
                          key={idx} 
                          className={cn(
                            "p-3 rounded-md border",
                            getSignificanceColor(diff.significance)
                          )}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">{diff.option}</span>
                            <Badge variant="outline" className={cn(
                              "text-xs",
                              diff.significance === 'high' ? "border-red-200" : 
                              diff.significance === 'medium' ? "border-yellow-200" : "border-blue-200"
                            )}>
                              {diff.difference.toFixed(1)}% difference
                            </Badge>
                          </div>
                          <p className="text-sm">
                            <span className="font-medium">Demographics:</span> {diff.demographics.join(' vs ')}
                          </p>
                        </div>
                      ))}
                  </div>
                  
                  <div className="pt-3 border-t">
                    <h4 className="text-sm font-medium mb-2">Understanding Differences</h4>
                    <p className="text-xs text-muted-foreground">
                      <span className="inline-block px-2 py-0.5 rounded bg-red-50 text-red-600 mr-1">High</span>
                      differences (15%+) indicate strong demographic divides
                    </p>
                    <p className="text-xs text-muted-foreground">
                      <span className="inline-block px-2 py-0.5 rounded bg-yellow-50 text-yellow-600 mr-1">Medium</span>
                      differences (10-15%) show notable demographic patterns
                    </p>
                    <p className="text-xs text-muted-foreground">
                      <span className="inline-block px-2 py-0.5 rounded bg-blue-50 text-blue-600 mr-1">Low</span>
                      differences (5-10%) suggest subtle demographic variations
                    </p>
                  </div>
                </div>
              ) : (
                <div className="py-8 text-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">
                    No significant demographic differences detected
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    This suggests strong consensus across all demographic groups
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Performance tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                Detailed performance metrics for this batch simulation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Total Demographics</h3>
                    <p className="text-2xl font-bold">{batchResult.results.length}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Total Responses</h3>
                    <p className="text-2xl font-bold">{batchResult.metadata.totalResponses.toLocaleString()}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Completion Time</h3>
                    <p className="text-lg">
                      {new Date(batchResult.metadata.completedAt).toLocaleString()}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Estimated Cost</h3>
                    <p className="text-lg">
                      ${batchResult.metadata.totalCost?.toFixed(2) || '0.00'}
                    </p>
                  </div>
                </div>
                
                {/* Cache performance metrics */}
                {batchResult.metadata.cachePerformance && (
                  <div className="mt-4 border-t pt-4">
                    <h3 className="text-sm font-medium mb-2">Cache Performance</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-xs text-muted-foreground">Cache Hit Rate</p>
                        <p className="text-lg font-medium">
                          {(batchResult.metadata.cachePerformance.hitRate * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-xs text-muted-foreground">Time Saved</p>
                        <p className="text-lg font-medium">
                          {batchResult.metadata.cachePerformance.timeSavedSeconds}s
                        </p>
                      </div>
                    </div>
                    
                    {batchResult.metadata.cachePerformance.cachedDemographics && 
                     batchResult.metadata.cachePerformance.cachedDemographics.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">Cached Demographics</p>
                        <div className="flex flex-wrap gap-1">
                          {batchResult.metadata.cachePerformance.cachedDemographics.map((demo, i) => (
                            <Badge key={i} variant="outline" className="text-emerald-600 border-emerald-200 bg-emerald-50">
                              {demo}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
