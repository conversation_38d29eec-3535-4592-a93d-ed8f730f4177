'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

// We'll use the standard Supabase client for all operations

type AuthContextType = {
  user: User | null
  session: Session | null
  loading: boolean
  isNewUser: boolean
  setIsNewUser: (value: boolean) => void
  signUp: (email: string, password: string, name: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  signIn: (email: string, password: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{
    error: Error | null
    success: boolean
  }>
  completeOnboarding: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Add this useAuth hook export
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Use Broadcast Channel for cross-tab communication
let authChannel: BroadcastChannel | null = null;

if (typeof window !== 'undefined') {
  authChannel = new BroadcastChannel('supabase-auth');
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isNewUser, setIsNewUser] = useState(false)
  const [envVarsAvailable, setEnvVarsAvailable] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check if environment variables are available
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn('Supabase URL or Anon Key is missing. Check your environment variables.')
      setEnvVarsAvailable(false)
      setLoading(false)
      return
    }

    // Verify local storage is working
    try {
      localStorage.setItem('auth_test', 'test');
      const testValue = localStorage.getItem('auth_test');
      localStorage.removeItem('auth_test');

      if (testValue !== 'test') {
        console.warn('LocalStorage is not working properly, which may affect authentication');
      }
    } catch (e) {
      console.error('LocalStorage access error:', e);
      setEnvVarsAvailable(false);
      setLoading(false);
      return;
    }

    // Get initial session with aggressive timeout and fallback
    const getInitialSession = async () => {
      try {
        console.log('[AuthProvider] Getting initial session...');

        // First try to get session from localStorage directly (much faster)
        let hasStoredSession = false;
        try {
          const authKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
          const storedAuth = localStorage.getItem(authKey);
          hasStoredSession = !!storedAuth;
          console.log('[AuthProvider] Found stored session:', hasStoredSession);
        } catch (e) {
          console.warn('[AuthProvider] Error checking localStorage:', e);
        }

        // If no stored session, skip the slow getSession call
        if (!hasStoredSession) {
          console.log('[AuthProvider] No stored session found, proceeding without auth');
          setSession(null);
          setUser(null);
          setLoading(false);
          return;
        }

        // Only call getSession if we have stored data, with very short timeout
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Session retrieval timeout')), 3000) // Reduced to 3 seconds
        );

        const result = await Promise.race([
          sessionPromise,
          timeoutPromise
        ]);

        const { data: { session }, error } = result as any;

        if (error) {
          console.warn('[AuthProvider] Session retrieval error:', error);
          setSession(null);
          setUser(null);
        } else if (session) {
          console.log('[AuthProvider] Session retrieved successfully');

          // Quick expiration check
          const now = Math.floor(Date.now() / 1000);
          if (session.expires_at && session.expires_at < now) {
            console.log('[AuthProvider] Session expired, clearing');
            setSession(null);
            setUser(null);
            // Don't await this - let it happen in background
            supabase.auth.signOut().catch(console.warn);
          } else {
            setSession(session);
            setUser(session.user);
          }
        } else {
          setSession(null);
          setUser(null);
        }
      } catch (error) {
        console.warn('[AuthProvider] Session retrieval failed:', error);
        // On any error, just proceed without session
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    }

    getInitialSession()

    // Listen for auth changes only if env vars are available
    if (envVarsAvailable) {
      const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, newSession) => {
        setSession(newSession)
        setUser(newSession?.user ?? null)

        // Broadcast auth state change to other tabs
        if (authChannel) {
          authChannel.postMessage({ type: 'auth_state_change', event, session: newSession });
        }

        // Force refresh server-side rendering data
        if (event === 'SIGNED_IN') {
          router.refresh()

          // Check if this is a new user by looking at their metadata
          if (newSession?.user) {
            try {
              // Start profile fetch immediately with a timeout
              const profilePromise = supabase
                .from('profiles')
                .select('*')
                .eq('id', newSession.user.id)
                .maybeSingle();

              // Set a timeout to navigate early if profile fetch is taking too long
              const timeoutPromise = new Promise<null>((resolve) => {
                setTimeout(() => {
                  // Navigate to dashboard anyway after timeout
                  if (window.location.pathname === '/login') {
                    // Check for redirect parameter
                    const urlParams = new URLSearchParams(window.location.search);
                    const redirectTo = urlParams.get('redirect');

                    if (redirectTo) {
                      try {
                        const decodedRedirect = decodeURIComponent(redirectTo);

                        // Enhanced safety checks to prevent redirect loops
                        if (
                          decodedRedirect.includes('login') ||
                          decodedRedirect.includes('register') ||
                          decodedRedirect.includes('auth/callback') ||
                          decodedRedirect === window.location.pathname
                        ) {
                          console.warn('Prevented redirect loop in timeout, redirecting to dashboard');
                          router.push('/dashboard');
                        } else {
                          router.push(decodedRedirect);
                        }
                      } catch (error) {
                        console.error('Error decoding redirect URL:', error);
                        router.push('/dashboard');
                      }
                    } else {
                      router.push('/dashboard');
                    }
                  }
                  resolve(null);
                }, 500); // Navigate after 500ms if profile fetch is slow
              });

              // Race between profile fetch and timeout
              const { data } = await Promise.race([
                profilePromise,
                timeoutPromise.then(() => profilePromise) // Still keep the profile promise running
              ]);

              // Check if user is new or existing
              if (data) {
                const createdAt = new Date(data.created_at);
                const now = new Date();
                const isRecentlyCreated = (now.getTime() - createdAt.getTime()) < 60000; // 1 minute
                setIsNewUser(isRecentlyCreated);

                // Navigate if we haven't navigated already from the timeout
                if (window.location.pathname === '/login') {
                  // Check for redirect parameter
                  const urlParams = new URLSearchParams(window.location.search);
                  const redirectTo = urlParams.get('redirect');

                  if (redirectTo) {
                    try {
                      const decodedRedirect = decodeURIComponent(redirectTo);

                      // Enhanced safety checks to prevent redirect loops
                      if (
                        decodedRedirect.includes('login') ||
                        decodedRedirect.includes('register') ||
                        decodedRedirect.includes('auth/callback') ||
                        decodedRedirect === window.location.pathname
                      ) {
                        console.warn('Prevented redirect loop in AuthProvider, redirecting to dashboard');
                        if (isRecentlyCreated) {
                          router.push('/dashboard?onboarding=true');
                        } else {
                          router.push('/dashboard');
                        }
                      } else {
                        if (isRecentlyCreated) {
                          // For new users, append onboarding parameter if not already a simulate page
                          const targetUrl = decodedRedirect.includes('/simulate')
                            ? decodedRedirect
                            : '/dashboard?onboarding=true';
                          router.push(targetUrl);
                        } else {
                          router.push(decodedRedirect);
                        }
                      }
                    } catch (error) {
                      console.error('Error decoding redirect URL:', error);
                      if (isRecentlyCreated) {
                        router.push('/dashboard?onboarding=true');
                      } else {
                        router.push('/dashboard');
                      }
                    }
                  } else {
                    if (isRecentlyCreated) {
                      router.push('/dashboard?onboarding=true');
                    } else {
                      router.push('/dashboard');
                    }
                  }
                }
              } else {
                // No profile found, treat as a new user
                setIsNewUser(true);

                // Create a profile for this user in the background
                supabase
                  .from('profiles')
                  .insert({
                    id: newSession.user.id,
                    email: newSession.user.email!,
                    name: newSession.user.user_metadata?.name || '',
                    created_at: new Date().toISOString(),
                  })
                  .then(({ error }) => {
                    if (error) console.error('Error creating profile:', error);
                  });

                // Navigate if we haven't navigated already from the timeout
                if (window.location.pathname === '/login') {
                  // Check for redirect parameter
                  const urlParams = new URLSearchParams(window.location.search);
                  const redirectTo = urlParams.get('redirect');

                  if (redirectTo) {
                    try {
                      const decodedRedirect = decodeURIComponent(redirectTo);

                      // Enhanced safety checks to prevent redirect loops
                      if (
                        decodedRedirect.includes('login') ||
                        decodedRedirect.includes('register') ||
                        decodedRedirect.includes('auth/callback') ||
                        decodedRedirect === window.location.pathname
                      ) {
                        console.warn('Prevented redirect loop in new user flow, redirecting to dashboard');
                        router.push('/dashboard?onboarding=true');
                      } else {
                        router.push(decodedRedirect);
                      }
                    } catch (error) {
                      console.error('Error decoding redirect URL:', error);
                      router.push('/dashboard?onboarding=true');
                    }
                  } else {
                    router.push('/dashboard?onboarding=true');
                  }
                }
              }
            } catch (error) {
              console.error('Error during profile check:', error);
              // Still navigate to dashboard in case of error
              if (window.location.pathname === '/login') {
                router.push('/dashboard');
              }
            }
          }
        }

        setLoading(false);

        // Auth state changed, no additional actions needed
      });

      // Listen for auth state changes from other tabs
      if (authChannel) {
        authChannel.onmessage = (event) => {
          if (event.data.type === 'auth_state_change') {
            console.log('[Website AuthProvider] Received auth state from other tab:', event.data.event);
            // Update session and user state based on the broadcasted data
            setSession(event.data.session);
            setUser(event.data.session?.user ?? null);
          }
        };
      }

      return () => {
        subscription.unsubscribe();
        if (authChannel) {
          authChannel.onmessage = null; // Clean up listener
        }
      }
    }
  }, [router, envVarsAvailable])

  // Minimal visibility change listener - only for essential coordination
  useEffect(() => {
    let lastVisibilityCheck = 0;

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        // Very aggressive rate limiting - only check once per minute
        const now = Date.now();
        if (now - lastVisibilityCheck < 60000) { // 60 second minimum between checks
          console.log('[AuthProvider] Skipping visibility check - too recent');
          return;
        }
        lastVisibilityCheck = now;

        // Skip session check for most pages to prevent slowdowns
        if (window.location.pathname.includes('/simulate') ||
            window.location.pathname.includes('/login') ||
            window.location.pathname.includes('/register') ||
            window.location.pathname.includes('/dashboard')) {
          console.log('[AuthProvider] Skipping session check for current page');
          return;
        }

        console.log('[AuthProvider] Tab became visible, minimal session check...');

        // Just broadcast that we're active - don't actually check session
        if (authChannel) {
          authChannel.postMessage({
            type: 'session_validation_complete',
            sessionValid: !!session, // Use current state
            timestamp: Date.now()
          });
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [session]); // Only depend on session, not router

  const signUp = async (email: string, password: string, name: string) => {
    try {
      // First create the auth user
      // Get the site URL from environment variables
      const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.pollgpt.com';

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
          emailRedirectTo: `${siteUrl}/auth/callback`,
        },
      });

      if (error) {
        console.error('Error during sign up:', error);
        return { error, success: false };
      }

      // Return success immediately after user creation
      // This prevents the UI from hanging while profile is created
      if (data?.user) {
        // Start profile creation in the background
        // We don't await this promise to avoid blocking the UI
        Promise.resolve().then(async () => {
          try {
            const { data: existingProfile } = await supabase
              .from('profiles')
              .select('id')
              .eq('id', data.user.id)
              .single();

            if (!existingProfile) {
              const { error: profileError } = await supabase
                .from('profiles')
                .insert({
                  id: data.user.id,
                  email: data.user.email!,
                  name: name,
                  created_at: new Date().toISOString(),
                });

              if (profileError) {
                console.error('Profile creation error:', profileError);
              }
            }
          } catch (err) {
            console.error('Error in background profile creation:', err);
          }
        });
      }

      // Return immediately without waiting for the background profile creation
      return { error: null, success: true };
    } catch (error) {
      console.error('Error during sign up:', error);
      return { error: error as Error, success: false };
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      return { error: null, success: true }
    } catch (error) {
      console.error('Error during sign in:', error)
      return { error: error as Error, success: false }
    }
  }

  const signOut = async () => {
    try {
      console.log('[AuthProvider] Signing out user...');

      // First clear session and user state in our context
      setSession(null);
      setUser(null);

      // Clear any additional auth-related data from localStorage
      if (typeof window !== 'undefined') {
        try {
          // Clear Supabase-specific items from localStorage
          const storageKey = 'sb-sumruaeyfidjlssrmfrm-auth-token';
          localStorage.removeItem(storageKey);

          // Clear any other auth-related items
          localStorage.removeItem('supabase.auth.token');
          localStorage.removeItem('supabase.auth.expires_at');
          localStorage.removeItem('supabase.auth.refresh_token');

          // Clear cookies by setting expired date
          document.cookie.split(';').forEach(cookie => {
            const [name] = cookie.trim().split('=');
            if (name.includes('supabase') || name.includes('auth')) {
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            }
          });
        } catch (e) {
          console.error('[AuthProvider] Error clearing local storage during logout:', e);
        }
      }

      // Broadcast logout to other tabs
      if (authChannel) {
        authChannel.postMessage({ type: 'auth_state_change', event: 'SIGNED_OUT', session: null });
      }

      // Call Supabase signOut AFTER clearing local state
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[AuthProvider] Error during sign out:', error);
        // Continue with redirect even if there's an error
      }

      console.log('[AuthProvider] User signed out successfully');

      // Use replace instead of push to prevent back navigation to authenticated pages
      router.replace('/login');
    } catch (error) {
      console.error('[AuthProvider] Unexpected error during sign out:', error);
      // Still try to redirect even if there was an error
      router.replace('/login');
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) throw error

      return { error: null, success: true }
    } catch (error) {
      console.error('Error during password reset:', error)
      return { error: error as Error, success: false }
    }
  }

  const completeOnboarding = () => {
    setIsNewUser(false);
  };

  const value = {
    user,
    session,
    loading,
    isNewUser,
    setIsNewUser,
    signUp,
    signIn,
    signOut,
    resetPassword,
    completeOnboarding,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}