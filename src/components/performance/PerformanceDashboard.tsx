'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Activity,
  Database,
  Zap,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Server,
  BarChart3,
  Timer,
  Database as Cache
} from 'lucide-react';

/**
 * Performance Monitoring Dashboard
 * Real-time performance metrics and system health monitoring
 */

interface MetricCardProps {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  status?: 'good' | 'warning' | 'critical';
  icon: React.ReactNode;
  description?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  unit,
  trend,
  status = 'good',
  icon,
  description
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'critical': return 'border-red-500 bg-red-50';
      case 'warning': return 'border-yellow-500 bg-yellow-50';
      default: return 'border-green-500 bg-green-50';
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'critical': return <Badge variant="destructive">Critical</Badge>;
      case 'warning': return <Badge variant="secondary">Warning</Badge>;
      default: return <Badge variant="default">Good</Badge>;
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;

    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
      default: return <div className="h-4 w-4" />;
    }
  };

  return (
    <Card className={`${getStatusColor()}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="flex items-center space-x-2">
          {getTrendIcon()}
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold">
              {value}
              {unit && <span className="text-sm font-normal text-muted-foreground ml-1">{unit}</span>}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          {getStatusBadge()}
        </div>
      </CardContent>
    </Card>
  );
};

interface PerformanceData {
  database: {
    connections: { active: number; idle: number; total: number };
    queryStats: { avgDuration: number; slowQueries: number; totalQueries: number };
  };
  cache: {
    hitRatio: number;
    memoryUsage: number;
    operations: number;
  };
  jobs: {
    queued: number;
    processing: number;
    completed: number;
    failed: number;
  };
  api: {
    requestsPerSecond: number;
    avgResponseTime: number;
    errorRate: number;
  };
  system: {
    memoryUsage: number;
    cpuUsage: number;
    uptime: number;
  };
  alerts: Array<{
    id: string;
    level: 'info' | 'warning' | 'critical';
    title: string;
    message: string;
    timestamp: string;
  }>;
}

const PerformanceDashboard: React.FC = () => {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchPerformanceData = async () => {
    try {
      setError(null);
      const response = await fetch('/api/performance/metrics');

      if (!response.ok) {
        throw new Error('Failed to fetch performance data');
      }

      const performanceData = await response.json();
      setData(performanceData);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Failed to fetch performance data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchPerformanceData, 30000); // 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getSystemStatus = (): 'good' | 'warning' | 'critical' => {
    if (!data) return 'good';

    const criticalConditions = [
      data.database.connections.active > 8,
      data.cache.hitRatio < 0.7,
      data.api.errorRate > 0.05,
      data.system.memoryUsage > 0.9,
    ];

    const warningConditions = [
      data.database.connections.active > 6,
      data.cache.hitRatio < 0.8,
      data.api.errorRate > 0.02,
      data.system.memoryUsage > 0.7,
      data.api.avgResponseTime > 2000,
    ];

    if (criticalConditions.some(Boolean)) return 'critical';
    if (warningConditions.some(Boolean)) return 'warning';
    return 'good';
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading performance metrics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load performance metrics: {error}
          <Button
            variant="outline"
            size="sm"
            className="ml-2"
            onClick={fetchPerformanceData}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>No performance data available</AlertDescription>
      </Alert>
    );
  }

  const systemStatus = getSystemStatus();

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time system performance and health monitoring
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`h-3 w-3 rounded-full ${
              systemStatus === 'good' ? 'bg-green-500' :
              systemStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
            <span className="text-sm font-medium">
              System {systemStatus === 'good' ? 'Healthy' :
                     systemStatus === 'warning' ? 'Warning' : 'Critical'}
            </span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPerformanceData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? <CheckCircle className="h-4 w-4 mr-2" /> : <XCircle className="h-4 w-4 mr-2" />}
            Auto Refresh
          </Button>
        </div>
      </div>

      {/* Last Updated */}
      {lastUpdated && (
        <p className="text-sm text-muted-foreground">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </p>
      )}

      {/* Alerts */}
      {data.alerts && data.alerts.length > 0 && (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Active Alerts</h2>
          {data.alerts.map((alert) => (
            <Alert key={alert.id} className={
              alert.level === 'critical' ? 'border-red-500' :
              alert.level === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <div>
                    <strong>{alert.title}</strong>
                    <p className="mt-1">{alert.message}</p>
                  </div>
                  <Badge variant={
                    alert.level === 'critical' ? 'destructive' :
                    alert.level === 'warning' ? 'secondary' : 'default'
                  }>
                    {alert.level}
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="cache">Cache</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
          <TabsTrigger value="api">API</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Database Connections"
              value={data.database.connections.active}
              unit={`/ ${data.database.connections.total}`}
              status={data.database.connections.active > 8 ? 'critical' :
                     data.database.connections.active > 6 ? 'warning' : 'good'}
              icon={<Database className="h-4 w-4" />}
              description="Active connections"
            />

            <MetricCard
              title="Cache Hit Ratio"
              value={`${(data.cache.hitRatio * 100).toFixed(1)}`}
              unit="%"
              status={data.cache.hitRatio < 0.7 ? 'critical' :
                     data.cache.hitRatio < 0.8 ? 'warning' : 'good'}
              icon={<Cache className="h-4 w-4" />}
              description="Cache efficiency"
            />

            <MetricCard
              title="Jobs Queued"
              value={data.jobs.queued}
              status={data.jobs.queued > 100 ? 'warning' : 'good'}
              icon={<Activity className="h-4 w-4" />}
              description="Pending jobs"
            />

            <MetricCard
              title="API Response Time"
              value={data.api.avgResponseTime}
              unit="ms"
              status={data.api.avgResponseTime > 2000 ? 'critical' :
                     data.api.avgResponseTime > 1000 ? 'warning' : 'good'}
              icon={<Timer className="h-4 w-4" />}
              description="Average response time"
            />
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <MetricCard
              title="Active Connections"
              value={data.database.connections.active}
              icon={<Database className="h-4 w-4" />}
              status={data.database.connections.active > 8 ? 'critical' : 'good'}
            />

            <MetricCard
              title="Idle Connections"
              value={data.database.connections.idle}
              icon={<Database className="h-4 w-4" />}
            />

            <MetricCard
              title="Total Queries"
              value={data.database.queryStats.totalQueries}
              icon={<BarChart3 className="h-4 w-4" />}
            />

            <MetricCard
              title="Avg Query Duration"
              value={data.database.queryStats.avgDuration}
              unit="ms"
              icon={<Clock className="h-4 w-4" />}
              status={data.database.queryStats.avgDuration > 100 ? 'warning' : 'good'}
            />

            <MetricCard
              title="Slow Queries"
              value={data.database.queryStats.slowQueries}
              icon={<AlertTriangle className="h-4 w-4" />}
              status={data.database.queryStats.slowQueries > 5 ? 'warning' : 'good'}
            />
          </div>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <MetricCard
              title="Hit Ratio"
              value={`${(data.cache.hitRatio * 100).toFixed(1)}`}
              unit="%"
              icon={<Cache className="h-4 w-4" />}
              status={data.cache.hitRatio < 0.8 ? 'warning' : 'good'}
            />

            <MetricCard
              title="Memory Usage"
              value={formatBytes(data.cache.memoryUsage)}
              icon={<Server className="h-4 w-4" />}
            />

            <MetricCard
              title="Operations"
              value={data.cache.operations}
              icon={<Activity className="h-4 w-4" />}
            />
          </div>
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Queued"
              value={data.jobs.queued}
              icon={<Clock className="h-4 w-4" />}
              status={data.jobs.queued > 100 ? 'warning' : 'good'}
            />

            <MetricCard
              title="Processing"
              value={data.jobs.processing}
              icon={<Zap className="h-4 w-4" />}
            />

            <MetricCard
              title="Completed"
              value={data.jobs.completed}
              icon={<CheckCircle className="h-4 w-4" />}
            />

            <MetricCard
              title="Failed"
              value={data.jobs.failed}
              icon={<XCircle className="h-4 w-4" />}
              status={data.jobs.failed > 10 ? 'warning' : 'good'}
            />
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <MetricCard
              title="Requests/sec"
              value={data.api.requestsPerSecond}
              icon={<Activity className="h-4 w-4" />}
            />

            <MetricCard
              title="Avg Response Time"
              value={data.api.avgResponseTime}
              unit="ms"
              icon={<Timer className="h-4 w-4" />}
              status={data.api.avgResponseTime > 1000 ? 'warning' : 'good'}
            />

            <MetricCard
              title="Error Rate"
              value={`${(data.api.errorRate * 100).toFixed(2)}`}
              unit="%"
              icon={<AlertTriangle className="h-4 w-4" />}
              status={data.api.errorRate > 0.05 ? 'critical' :
                     data.api.errorRate > 0.02 ? 'warning' : 'good'}
            />
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <MetricCard
              title="Memory Usage"
              value={`${(data.system.memoryUsage * 100).toFixed(1)}`}
              unit="%"
              icon={<Server className="h-4 w-4" />}
              status={data.system.memoryUsage > 0.9 ? 'critical' :
                     data.system.memoryUsage > 0.7 ? 'warning' : 'good'}
            />

            <MetricCard
              title="CPU Usage"
              value={`${data.system.cpuUsage.toFixed(1)}`}
              unit="%"
              icon={<Activity className="h-4 w-4" />}
              status={data.system.cpuUsage > 90 ? 'critical' :
                     data.system.cpuUsage > 70 ? 'warning' : 'good'}
            />

            <MetricCard
              title="Uptime"
              value={formatUptime(data.system.uptime)}
              icon={<Clock className="h-4 w-4" />}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceDashboard;
