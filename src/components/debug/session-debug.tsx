'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/providers/auth-provider'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { clearCorruptedSession } from '@/lib/utils/auth-refresh'

interface SessionInfo {
  hasSession: boolean
  isExpired: boolean
  expiresAt?: number
  userId?: string
  email?: string
  lastRefresh?: number
  error?: string
}

export function SessionDebugger() {
  const { user, session, loading } = useAuth()
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  const checkSessionDetails = async () => {
    try {
      const { data, error } = await supabase.auth.getSession()
      
      if (error) {
        setSessionInfo({
          hasSession: false,
          isExpired: false,
          error: error.message
        })
        return
      }

      const now = Math.floor(Date.now() / 1000)
      const isExpired = data.session?.expires_at ? data.session.expires_at < now : false

      setSessionInfo({
        hasSession: !!data.session,
        isExpired,
        expiresAt: data.session?.expires_at,
        userId: data.session?.user?.id,
        email: data.session?.user?.email,
        lastRefresh: data.session?.refresh_token ? now : undefined
      })
    } catch (error) {
      setSessionInfo({
        hasSession: false,
        isExpired: false,
        error: (error as Error).message
      })
    }
  }

  useEffect(() => {
    if (!loading) {
      checkSessionDetails()
    }
  }, [loading, user, session])

  const handleClearSession = async () => {
    await clearCorruptedSession()
    await checkSessionDetails()
  }

  // Only show in development or when there are issues
  if (process.env.NODE_ENV === 'production' && !sessionInfo?.error) {
    return null
  }

  return (
    <div 
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        color: 'white',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '12px',
        zIndex: 9999,
        maxWidth: '300px',
        fontFamily: 'monospace'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <strong>Session Debug</strong>
        <button
          onClick={() => setIsVisible(!isVisible)}
          style={{
            backgroundColor: 'transparent',
            color: 'white',
            border: '1px solid white',
            borderRadius: '4px',
            padding: '2px 6px',
            cursor: 'pointer'
          }}
        >
          {isVisible ? 'Hide' : 'Show'}
        </button>
      </div>
      
      {isVisible && (
        <div style={{ fontSize: '11px', lineHeight: '1.4' }}>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
          <div>User: {user ? `${user.email} (${user.id.substring(0, 8)}...)` : 'None'}</div>
          <div>Context Session: {session ? 'Active' : 'None'}</div>
          
          {sessionInfo && (
            <>
              <hr style={{ margin: '8px 0', borderColor: '#444' }} />
              <div>Direct Session: {sessionInfo.hasSession ? 'Active' : 'None'}</div>
              {sessionInfo.hasSession && (
                <>
                  <div>Expired: {sessionInfo.isExpired ? 'Yes' : 'No'}</div>
                  {sessionInfo.expiresAt && (
                    <div>Expires: {new Date(sessionInfo.expiresAt * 1000).toLocaleTimeString()}</div>
                  )}
                </>
              )}
              {sessionInfo.error && (
                <div style={{ color: '#ff6b6b' }}>Error: {sessionInfo.error}</div>
              )}
            </>
          )}
          
          <div style={{ marginTop: '10px', display: 'flex', gap: '5px' }}>
            <Button
              onClick={checkSessionDetails}
              size="sm"
              variant="outline"
              style={{ fontSize: '10px', padding: '2px 6px' }}
            >
              Refresh
            </Button>
            <Button
              onClick={handleClearSession}
              size="sm"
              variant="destructive"
              style={{ fontSize: '10px', padding: '2px 6px' }}
            >
              Clear
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
