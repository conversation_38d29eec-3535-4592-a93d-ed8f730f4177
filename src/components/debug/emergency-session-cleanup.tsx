'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { clearCorruptedSession } from '@/lib/utils/auth-refresh'

export function EmergencySessionCleanup() {
  const [isClearing, setIsClearing] = useState(false)

  const handleEmergencyCleanup = async () => {
    if (!confirm('This will clear all session data and redirect to login. Continue?')) {
      return
    }

    setIsClearing(true)
    try {
      // Clear all auth-related data
      await clearCorruptedSession()
      
      // Clear additional items that might be causing issues
      const additionalKeys = [
        'pollgpt_polls_cache',
        'pollgpt_retry_after_refresh',
        'supabase.auth.token',
        'sb-sumruaeyfidjlssrmfrm-auth-token-code-verifier'
      ]
      
      additionalKeys.forEach(key => {
        try {
          localStorage.removeItem(key)
          sessionStorage.removeItem(key)
        } catch (e) {
          console.warn(`Failed to remove ${key}:`, e)
        }
      })

      // Clear all cookies
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      console.log('Emergency session cleanup completed')
      
      // Force reload to clear any in-memory state
      window.location.href = '/login'
    } catch (error) {
      console.error('Emergency cleanup failed:', error)
      // Still try to redirect
      window.location.href = '/login'
    }
  }

  // Only show if there are performance issues
  const shouldShow = typeof window !== 'undefined' && (
    performance.now() > 10000 || // Page has been loading for more than 10 seconds
    localStorage.getItem('pollgpt_emergency_mode') === 'true'
  )

  if (!shouldShow) {
    return null
  }

  return (
    <div 
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        backgroundColor: '#dc2626',
        color: 'white',
        padding: '12px',
        borderRadius: '8px',
        zIndex: 10000,
        maxWidth: '300px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
      }}
    >
      <div style={{ marginBottom: '8px', fontWeight: 'bold', fontSize: '14px' }}>
        🚨 Performance Issues Detected
      </div>
      <div style={{ marginBottom: '12px', fontSize: '12px', lineHeight: '1.4' }}>
        If the app is running slowly or hanging, this emergency cleanup can help.
      </div>
      <Button
        onClick={handleEmergencyCleanup}
        disabled={isClearing}
        size="sm"
        variant="destructive"
        style={{ 
          width: '100%', 
          backgroundColor: 'white', 
          color: '#dc2626',
          fontSize: '12px'
        }}
      >
        {isClearing ? 'Cleaning...' : 'Emergency Session Reset'}
      </Button>
    </div>
  )
}
