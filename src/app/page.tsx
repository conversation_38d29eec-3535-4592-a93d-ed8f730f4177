"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeLogo } from "@/components/ui/theme-logo";
import { PollCreationDemo } from "@/components/ui/poll-creation-demo";
import { AiCreationPreview, DistributionPreview, RealTimeResponsesPreview, AnalyticsPreview } from "@/components/ui/feature-previews";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { NewsletterForm } from "@/components/ui/newsletter-form";
import { AlternativeNames, RichKeywords } from "@/components/ui/seo-helpers";
import { SEOConstruct } from "@/components/ui/seo-construct";
// Import the useAuth hook from the auth-provider
import { useAuth } from "@/components/providers/auth-provider";
import { PageMetadata } from "./page-metadata";
import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { SEOFaq } from "@/components/ui/seo-faq";
import { SEOFaqEnhanced } from "@/components/ui/seo-faq-enhanced";
import { LinkArchive } from "@/components/ui/link-archive";

export default function Home() {
  const [isAuthLoaded, setIsAuthLoaded] = useState(false);
  const [authUser, setAuthUser] = useState<User | null>(null);
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const auth = useAuth();

  useEffect(() => {
    // Handle auth state changes
    if (auth) {
      setIsAuthLoaded(true);
      setAuthUser(auth.user);
      setIsAuthLoading(auth.loading);
    }
  }, [auth]);

  // If auth is still loading and we've waited less than 2 seconds, show loading
  // Otherwise proceed with the page even if auth isn't ready
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAuthLoading(false);
    }, 2000); // After 2 seconds show the page regardless of auth state

    return () => clearTimeout(timer);
  }, []);

  // We'll rely on middleware for redirections
  // Show loading state while auth state is being determined
  if (isAuthLoading && !isAuthLoaded) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-pulse-slow flex flex-col items-center">
        <ThemeLogo width={40} height={40} className="h-10 w-auto" type="icon" />
        <p className="mt-4 text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>;
  }

  return (
    <div className="flex min-h-screen flex-col relative overflow-hidden">
      <PageMetadata />
      {/* Background Elements - adjusted for better visibility */}
      <div className="geometric-overlay absolute inset-0 z-0"></div>
      <div className="floating-particles absolute inset-0 z-0">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>

      {/* Header with logo, login/dashboard buttons and theme toggle */}
      <header className="w-full py-4 relative z-10">
        <div className="container px-4 md:px-6 mx-auto flex justify-between items-center">
          <Link href={authUser ? "/dashboard/polls" : "/"} className="flex items-center gap-2">
            <div className="flex items-center" style={{ zIndex: 100 }}>
              <ThemeLogo
                width={24}
                height={24}
                className="h-6 w-auto animate-pulse-slow"
                type="logo"
              />
            </div>
          </Link>
          <div className="flex items-center gap-2">
            {authUser ? (
              <Button asChild variant="default" size="sm">
                <Link href="/dashboard/polls">Go to Dashboard</Link>
              </Button>
            ) : (
              <>
                <Button asChild variant="ghost" size="sm">
                  <Link href="/login">Login</Link>
                </Button>
                <Button asChild variant="default" size="sm">
                  <Link href="/register">Sign Up</Link>
                </Button>
              </>
            )}
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="w-full py-12 md:py-24 lg:py-32 relative z-10">
        <div className="container px-4 md:px-6 mx-auto flex flex-col items-center text-center space-y-8">
          {/* Hidden SEO text for search engines */}
          <AlternativeNames />
          <div className="flex flex-col items-center gap-4">
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl animate-fadeInUp">
              Create intelligent polls with <span className="text-primary relative inline-block">
                AI assistance
                <span className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-40"></span>
              </span>
            </h1>
          </div>
          <p className="max-w-[700px] text-muted-foreground md:text-xl animate-fadeInUpDelayed">
            Streamline your polling process from creation to analysis using PollGPT. Generate unbiased questions, collect responses, and gain actionable insights with minimal effort.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 animate-fadeInUpMoreDelayed">
            <Button asChild size="lg" className="w-full sm:w-auto relative overflow-hidden group">
              <Link href="/register">
                Get Started
                <span className="absolute top-0 left-0 w-full h-full bg-white opacity-0 group-hover:opacity-10 transition-opacity"></span>
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
              <Link href="/#features">Learn More</Link>
            </Button>
          </div>

          {/* Interactive Demo Section */}
          <div className="relative w-full max-w-3xl mt-8 animate-fadeInUpMostDelayed z-20">
            <div className="absolute -top-3 left-1/2 z-31 dark:text-black transform -translate-x-1/2 bg-black dark:bg-white text-primary-foreground px-4 py-1 rounded-full text-sm font-medium shadow-md ">
              Live Demo
            </div>
            <div className="card-glow absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent rounded-xl blur-3xl opacity-30 z-0"></div>
            <div className="relative z-30">
              <PollCreationDemo />
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" className="w-full py-12 md:py-24 bg-muted/50 relative z-10">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Core Features
            </h2>
            <p className="max-w-[600px] text-muted-foreground md:text-lg">
              Everything you need to create, distribute, and analyze polls with AI assistance.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Feature 1: AI-Assisted Creation */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                    <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-5 0v-15A2.5 2.5 0 0 1 9.5 2Z"></path>
                    <path d="M14.5 8A2.5 2.5 0 0 1 17 10.5v9a2.5 2.5 0 0 1-5 0v-9A2.5 2.5 0 0 1 14.5 8Z"></path>
                    <path d="M4.5 14a2.5 2.5 0 0 1 5 0v5a2.5 2.5 0 0 1-5 0v-5Z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold">AI-Assisted Creation</h3>
              </div>
              <p className="text-muted-foreground">Generate intelligent poll questions based on your topic and audience. Our AI analyzes your input and creates balanced, engaging questions.</p>
              <AiCreationPreview />
            </div>

            {/* Feature 2: Easy Distribution */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Easy Distribution</h3>
              </div>
              <p className="text-muted-foreground">Share polls with custom links, QR codes, or embed them on your website. Reach your audience wherever they are with just a few clicks.</p>
              <DistributionPreview />
            </div>

            {/* Feature 3: Real-time Responses */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                    <path d="M20 7h-9"></path>
                    <path d="M14 17H5"></path>
                    <circle cx="17" cy="17" r="3"></circle>
                    <circle cx="7" cy="7" r="3"></circle>
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Real-time Responses</h3>
              </div>
              <p className="text-muted-foreground">Track responses instantly as they come in with live updates. Watch your data grow in real-time and get immediate feedback from your audience.</p>
              <RealTimeResponsesPreview />
            </div>

            {/* Feature 4: Advanced Analytics */}
            <div className="space-y-4 transform transition-transform hover:-translate-y-1 duration-300">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-primary/10 text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                    <path d="M3 3v18h18"></path>
                    <path d="m19 9-5 5-4-4-3 3"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Advanced Analytics</h3>
              </div>
              <p className="text-muted-foreground">Get AI-powered insights and visualizations from your poll results. Uncover patterns, trends, and actionable recommendations automatically.</p>
              <AnalyticsPreview />
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="w-full py-12 md:py-24 relative z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent z-0"></div>
        <div className="container px-4 md:px-6 mx-auto flex flex-col items-center text-center space-y-8 relative z-10">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Ready to create better polls?</h2>
          <p className="max-w-[600px] text-muted-foreground md:text-lg">Join thousands of researchers, content creators, and business leaders using PollGPT.</p>
          <Button asChild size="lg" className="relative overflow-hidden group">
            <Link href="/register">
              Create Your First Poll
              <span className="absolute top-0 left-0 w-full h-full bg-white opacity-0 group-hover:opacity-10 transition-opacity"></span>
            </Link>
          </Button>
        </div>
      </div>
     {/* Enhanced FAQ section - Visible and optimized for SEO */}
      <SEOFaqEnhanced />
      {/* Subscribe Section */}
      <div className="w-full py-16 md:py-20 bg-muted relative z-10">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Stay updated with PollGPT</h2>
                <p className="text-muted-foreground text-lg">Subscribe to our newsletter for the latest features, tips, and polling best practices. No spam, ever.</p>
                <div className="flex items-center gap-3 mt-4">
                  <div className="p-3 rounded-full bg-primary/10 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <p className="text-sm">Join <span className="font-medium">2,000+</span> poll creators &amp; researchers</p>
                </div>
              </div>
              <div className="bg-background/90 backdrop-blur-sm rounded-lg p-8 shadow-sm border border-border/30 md:ml-auto md:max-w-md w-full">
                <NewsletterForm />
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Semantic term definitions for SEO */}
      <SEOConstruct />

      {/* Legacy SEO FAQ - Hidden but still detected by search engines */}
      <SEOFaq />

      {/* Rich Keywords section for SEO */}
      <RichKeywords />

      {/* Link Archive for internal linking SEO */}
      <LinkArchive />

      {/* Footer */}
      <div className="w-full py-6 bg-muted relative z-10">
        <div className="container px-4 md:px-6 mx-auto text-sm text-muted-foreground">
          {/* Mobile layout - stacked vertically */}
          <div className="flex flex-col space-y-4 md:hidden">
            <div className="flex flex-col items-center space-y-3">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
              <div className="flex flex-col space-y-2">
                <a href="mailto:<EMAIL>" className="flex items-center justify-center gap-2 hover:text-foreground transition-colors" title="Email us">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center justify-center gap-2 not-italic hover:text-foreground text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                    <circle cx="12" cy="10" r="3"/>
                  </svg>
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex justify-center gap-6">
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Help</Link>
            </nav>
          </div>

          {/* Desktop layout - side by side */}
          <div className="hidden md:flex items-center justify-between gap-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4">
              <div className="flex items-center gap-2">
                <ThemeLogo type="icon" width={32} height={32} className="h-8 w-8" />
                <span className="font-bold text-lg text-foreground">PollGPT</span>
              </div>
              <div className="flex flex-col lg:flex-row gap-2 lg:gap-4">
                <a href="mailto:<EMAIL>" className="flex items-center gap-1 hover:text-foreground transition-colors" title="Email us">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  <span><EMAIL></span>
                </a>
                <address className="flex items-center gap-1 not-italic hover:text-foreground">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                    <circle cx="12" cy="10" r="3"/>
                  </svg>
                  <span>5 Parv. Alan Turing, 75013 Paris, France</span>
                </address>
              </div>
            </div>
            <nav className="flex gap-4">
              <Link href="/" className="hover:text-foreground transition-colors">Terms</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Privacy</Link>
              <Link href="/" className="hover:text-foreground transition-colors">Help</Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
