import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { createClient } from '@supabase/supabase-js';
// Import the simulatePoll function from the ESM version of the module
import { simulatePoll } from '@/lib/services/perplexity-ai';
import { Database } from '@/lib/database.types';
import { SimulationRequest, SimulationResponse } from '@/lib/types/simulation';

export const runtime = 'edge';

// Simple in-memory rate limiting (for development)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration for simulations
const SIMULATION_RATE_LIMITS = {
  requests: 10, // 10 simulations per hour
  windowMs: 60 * 60 * 1000, // 1 hour
};

function checkRateLimit(userId: string): { allowed: boolean; remaining: number; retryAfter?: number } {
  const now = Date.now();
  const key = `simulation:${userId}`;
  const current = rateLimitMap.get(key);

  // Clean up expired entries
  if (current && now > current.resetTime) {
    rateLimitMap.delete(key);
  }

  const limit = rateLimitMap.get(key);

  if (!limit) {
    // First request in window
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + SIMULATION_RATE_LIMITS.windowMs
    });
    return { allowed: true, remaining: SIMULATION_RATE_LIMITS.requests - 1 };
  }

  if (limit.count >= SIMULATION_RATE_LIMITS.requests) {
    return {
      allowed: false,
      remaining: 0,
      retryAfter: Math.ceil((limit.resetTime - now) / 1000)
    };
  }

  limit.count++;
  return { allowed: true, remaining: SIMULATION_RATE_LIMITS.requests - limit.count };
}

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for authentication
    const cookieStore = await cookies();
    
    // Regular client for user authentication
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll(),
          setAll: (cookiesToSet) => {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );
    
    // Check if we have the required environment variables for admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing required environment variables for admin Supabase client');
      return NextResponse.json(
        { error: 'Server configuration error. Missing required environment variables.' },
        { status: 500 }
      );
    }
    
    // Create a separate admin client for operations that need to bypass RLS
    const adminClient = createClient<Database>(supabaseUrl, serviceRoleKey);

    // Parse the request body
    const body = await request.json();
    const { pollQuestion, pollOptions, demographic, responseFormat = 'both' } = body;

    // Validate required fields
    if (!pollQuestion || !pollOptions || !demographic) {
      return NextResponse.json(
        { error: 'Missing required fields: pollQuestion, pollOptions, demographic' },
        { status: 400 }
      );
    }

    // Validate poll options
    if (!Array.isArray(pollOptions) || pollOptions.length < 2) {
      return NextResponse.json(
        { error: 'pollOptions must be an array with at least 2 options' },
        { status: 400 }
      );
    }

    // Validate demographic
    if (!demographic.group || !demographic.size) {
      return NextResponse.json(
        { error: 'demographic must include group and size' },
        { status: 400 }
      );
    }

    // Validate sample size
    if (demographic.size < 10 || demographic.size > 1000) {
      return NextResponse.json(
        { error: 'Sample size must be between 10 and 1000' },
        { status: 400 }
      );
    }

    // Get the user ID from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    // Log authentication details for debugging
    console.log('Authentication check:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      isProduction: process.env.NODE_ENV === 'production',
      timestamp: new Date().toISOString()
    });
    
    if (sessionError) {
      console.error('Session error:', sessionError);
    }
    
    // Use a default user ID for development if no session is available
    let userId = session?.user?.id;
    
    // In production, allow public access for simulation without login if PUBLIC_SIMULATIONS=true
    const allowPublicSimulations = process.env.PUBLIC_SIMULATIONS === 'true';
    
    if (!userId && process.env.NODE_ENV === 'development') {
      // In development, use a development user ID if no session
      userId = '00000000-0000-0000-0000-000000000000';
      console.log('Using development authentication bypass with user ID:', userId);
    } else if (!userId && allowPublicSimulations) {
      // For public simulations in production when enabled
      userId = 'public-simulation-user';
      console.log('Using public simulation access');
    }
    
    // If no user ID is available, return unauthorized with helpful message
    if (!userId) {
      return NextResponse.json(
        { 
          error: 'Unauthorized. Please sign in to use this feature.',
          details: 'Session validation failed. Try logging out and back in.',
          isAuthenticated: false
        },
        { status: 401 }
      );
    }

    // Apply rate limiting
    const rateLimitResult = checkRateLimit(userId);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter,
          message: `Too many simulation requests. Please try again in ${rateLimitResult.retryAfter} seconds.`
        },
        {
          status: 429,
          headers: {
            'Retry-After': rateLimitResult.retryAfter?.toString() || '3600'
          }
        }
      );
    }

    // Prepare the simulation request
    const simulationRequest: SimulationRequest = {
      pollQuestion,
      pollOptions,
      demographic: {
        group: demographic.group,
        size: demographic.size,
        context: demographic.context
      },
      responseFormat
    };

    console.log('Processing simulation request:', {
      question: pollQuestion,
      optionsCount: pollOptions.length,
      demographic: demographic.group,
      sampleSize: demographic.size,
      userId: userId
    });

    // Call the simulation service
    const simulationResponse: SimulationResponse = await simulatePoll(simulationRequest);

    // If poll_id is provided, store the simulation in the database
    if (body.poll_id) {
      try {
        // First, check if the poll exists and get the owner's ID
        const { data: pollData, error: pollError } = await supabase
          .from('polls')
          .select('user_id')
          .eq('id', body.poll_id)
          .single();
          
        if (pollError) {
          console.error('Error checking poll existence:', pollError);
        } else if (pollData) {
          // Use the actual poll owner's ID for created_by
          userId = pollData.user_id;
          console.log(`Using poll owner's ID (${userId}) for created_by`);
          
          // Use the admin client to bypass RLS policies
          const { error: insertError } = await adminClient
            .from('poll_simulations')
            .insert({
              poll_id: body.poll_id,
              demographic_group: demographic.group,
              sample_size: demographic.size,
              simulation_data: simulationResponse,
              confidence_score: simulationResponse.metadata.confidence,
              citations: simulationResponse.metadata.citations,
              created_by: userId
            });

          if (insertError) {
            console.error('Error storing simulation:', insertError);
            // Continue without failing the request
          } else {
            console.log('Successfully stored simulation for poll:', body.poll_id);
          }
        }
      } catch (dbError) {
        console.error('Database error storing simulation:', dbError);
        // Continue without failing the request
      }
    }

    // Return the simulation response
    return NextResponse.json({
      success: true,
      simulation: simulationResponse,
      metadata: {
        requestId: simulationResponse.simulationId,
        userId: userId,
        timestamp: new Date().toISOString(),
        rateLimitRemaining: rateLimitResult.remaining
      }
    });
  } catch (error) {
    console.error('Error processing simulation request:', error);
    return NextResponse.json(
      {
        error: 'Failed to process simulation request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve cached simulations
export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for authentication
    const cookieStore = await cookies();
    
    // Regular client for user authentication
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: () => cookieStore.getAll(),
          setAll: (cookiesToSet) => {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );
    
    // Check if we have the required environment variables for admin client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing required environment variables for admin Supabase client');
      return NextResponse.json(
        { error: 'Server configuration error. Missing required environment variables.' },
        { status: 500 }
      );
    }
    
    // Create a separate admin client for operations that need to bypass RLS
    const adminClient = createClient<Database>(supabaseUrl, serviceRoleKey);

    const { searchParams } = new URL(request.url);
    const pollId = searchParams.get('poll_id');
    const demographic = searchParams.get('demographic');

    if (!pollId) {
      return NextResponse.json(
        { error: 'Missing required parameter: poll_id' },
        { status: 400 }
      );
    }

    // Get the user ID from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    // Log authentication details for debugging
    console.log('GET Authentication check:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      isProduction: process.env.NODE_ENV === 'production',
      timestamp: new Date().toISOString()
    });
    
    if (sessionError) {
      console.error('GET Session error:', sessionError);
    }
    
    // Use a default user ID for development if no session is available
    let userId = session?.user?.id;
    
    // In production, allow public access for simulation without login if PUBLIC_SIMULATIONS=true
    const allowPublicSimulations = process.env.PUBLIC_SIMULATIONS === 'true';
    
    if (!userId && process.env.NODE_ENV === 'development') {
      // In development, use a development user ID if no session
      userId = '00000000-0000-0000-0000-000000000000';
      console.log('Using development authentication bypass for GET request');
    } else if (!userId && allowPublicSimulations) {
      // For public simulations in production when enabled
      userId = 'public-simulation-user';
      console.log('Using public simulation access for GET request');
    }
    
    // If no user ID is available, return unauthorized with helpful message
    if (!userId) {
      return NextResponse.json(
        { 
          error: 'Unauthorized. Please sign in to use this feature.',
          details: 'Session validation failed. Try logging out and back in.',
          isAuthenticated: false
        },
        { status: 401 }
      );
    }

    // Query for cached simulations - use adminClient to bypass RLS
    let query = adminClient
      .from('poll_simulations')
      .select('*')
      .eq('poll_id', pollId)
      .order('created_at', { ascending: false });

    if (demographic) {
      query = query.eq('demographic_group', demographic);
    }

    const { data, error } = await query.limit(10);

    if (error) {
      console.error('Error fetching cached simulations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch cached simulations' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      simulations: data
    });
  } catch (error) {
    console.error('Error fetching cached simulations:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch cached simulations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
