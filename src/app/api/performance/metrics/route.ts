import { NextResponse } from 'next/server';
import { PerformanceMonitor } from '@/lib/services/performance-monitor';

// Get the singleton instance
const performanceMonitor = PerformanceMonitor.getInstance();
// Import services from the index file
import { enhancedDb, redisCacheService, jobQueue } from '@/lib/services';

/**
 * Performance Metrics API Endpoint
 *
 * Provides real-time performance metrics for the dashboard:
 * - Database connection stats
 * - Cache performance metrics
 * - Job queue statistics
 * - API performance data
 * - System resource usage
 * - Active alerts
 */

export async function GET() {
  try {
    const startTime = Date.now();

    // Collect all performance metrics
    const [
      dbStats,
      cacheStats,
      jobStats,
      systemMetrics,
      alerts
    ] = await Promise.allSettled([
      getDatabaseMetrics(),
      getCacheMetrics(),
      getJobQueueMetrics(),
      getSystemMetrics(),
      getActiveAlerts()
    ]);

    // Process results and handle any failures gracefully
    const performanceData = {
      database: dbStats.status === 'fulfilled' ? dbStats.value : getDefaultDatabaseMetrics(),
      cache: cacheStats.status === 'fulfilled' ? cacheStats.value : getDefaultCacheMetrics(),
      jobs: jobStats.status === 'fulfilled' ? jobStats.value : getDefaultJobMetrics(),
      api: await getApiMetrics(),
      system: systemMetrics.status === 'fulfilled' ? systemMetrics.value : getDefaultSystemMetrics(),
      alerts: alerts.status === 'fulfilled' ? alerts.value : [],
      metadata: {
        timestamp: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        version: '1.0.0'
      }
    };

    // Record API metrics
    await recordApiMetrics(Date.now() - startTime, 'success');

    return NextResponse.json(performanceData, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('Error fetching performance metrics:', error);

    // Record error metrics
    await recordApiMetrics(Date.now() - Date.now(), 'error');

    return NextResponse.json(
      {
        error: 'Failed to fetch performance metrics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getDatabaseMetrics() {
  try {
    const poolStats = await enhancedDb.getPoolStats();
    const allQueryStats = await enhancedDb.getQueryStats(); // Gets all query stats as Record<string, QueryStatsObject>
    const slowQueries = await enhancedDb.getSlowQueries(10);

    let totalQueries = 0;
    let totalDurationSum = 0;
    let totalErrors = 0;

    Object.values(allQueryStats).forEach(stats => {
      // Type guard to ensure we have the right properties
      if (typeof stats === 'object' && stats !== null && 
          'count' in stats && 'totalTime' in stats && 'errors' in stats) {
        totalQueries += Number(stats.count);
        totalDurationSum += Number(stats.totalTime);
        totalErrors += Number(stats.errors);
      }
    });

    const averageDuration = totalQueries > 0 ? totalDurationSum / totalQueries : 0;
    const errorRate = totalQueries > 0 ? totalErrors / totalQueries : 0;

    // Placeholder for transaction stats as it's not directly available from general query stats
    const transactionStats = {
        totalTransactions: 0,
        avgCommitTime: 0,
        avgRollbackTime: 0,
    };
    // If 'transactions' is a specifically monitored query, you could get its stats:
    // const specificTransactionStats = allQueryStats['transaction_query_name'];
    // if (specificTransactionStats) { /* populate transactionStats */ }

    return {
      connectionPool: {
        totalConnections: poolStats.totalConnections || 0,
        activeConnections: poolStats.busyConnections || 0, // Corrected: busyConnections
        idleConnections: poolStats.availableConnections || 0, // Corrected: availableConnections
        maxConnections: poolStats.totalConnections || 0, // Assuming totalConnections reflects current max/capacity, or use a config value
        waitingRequests: 0, // Not directly available from getPoolStats
      },
      queryPerformance: {
        totalQueries: totalQueries,
        averageDuration: averageDuration, // in ms
        queriesPerSecond: 0, // Placeholder, needs time window context
        errorRate: errorRate,
        slowQueries: slowQueries || [],
      },
      transactionStats: transactionStats,
      // Add other relevant DB metrics as needed
    };
  } catch (error) {
    console.error('Error fetching database metrics:', error);
    return {
      connectionPool: { error: 'Failed to retrieve pool stats' },
      queryPerformance: { error: 'Failed to retrieve query stats' },
      transactionStats: { error: 'Failed to retrieve transaction stats' },
    };
  }
}

async function getCacheMetrics() {
  try {
    const cacheStats = await redisCacheService.getStats();

    // Calculate cache metrics from recent data
    const recentMetrics = await performanceMonitor.getMetrics(
      'cache.hits',
      new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
      new Date(),
      60
    );

    const recentMisses = await performanceMonitor.getMetrics(
      'cache.misses',
      new Date(Date.now() - 5 * 60 * 1000),
      new Date(),
      60
    );

    const totalHits = recentMetrics.reduce((sum, metric) => sum + metric.value, 0);
    const totalMisses = recentMisses.reduce((sum, metric) => sum + metric.value, 0);
    const totalOperations = totalHits + totalMisses;
    const hitRatio = totalOperations > 0 ? totalHits / totalOperations : 0.95; // Default to 95%

    return {
      hitRatio,
      memoryUsage: cacheStats.fallbackCacheStats?.calculatedSize || 0,
      operations: totalOperations,
      redisAvailable: cacheStats.redisAvailable,
      fallbackCacheSize: cacheStats.fallbackCacheSize,
      performance: {
        avgResponseTime: 2.5, // Default average cache response time
        throughput: totalOperations
      }
    };
  } catch (error) {
    console.error('Error getting cache metrics:', error);
    throw error;
  }
}

async function getJobQueueMetrics() {
  try {
    // jobQueue is now imported directly as an instance
    const stats = await jobQueue.getStats();

    return {
      queued: stats.pending || 0,
      processing: stats.processing || 0,
      completed: stats.completed || 0,
      failed: stats.failed || 0,
      delayed: stats.retrying || 0, // Using retrying as a proxy for delayed, or set to 0 if not applicable
      workers: stats.processing || 0, // Using processing as a proxy for active workers, or set based on actual worker tracking if available
      performance: {
        avgProcessingTime: 0, // Placeholder - not directly available from getStats
        throughput: 0 // Placeholder - not directly available from getStats
      }
    };
  } catch (error) {
    console.error('Error getting job queue metrics:', error);
    // Return default metrics if job queue is not available
    return {
      queued: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
      workers: 0,
      performance: {
        avgProcessingTime: 0,
        throughput: 0
      }
    };
  }
}

async function getApiMetrics() {
  try {
    // Get recent API metrics
    const recentRequests = await performanceMonitor.getMetrics(
      'api.requests',
      new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
      new Date(),
      60
    );

    const recentResponseTimes = await performanceMonitor.getMetrics(
      'api.response_time',
      new Date(Date.now() - 5 * 60 * 1000),
      new Date(),
      60
    );

    const recentErrors = await performanceMonitor.getMetrics(
      'api.errors',
      new Date(Date.now() - 5 * 60 * 1000),
      new Date(),
      60
    );

    const totalRequests = recentRequests.reduce((sum, metric) => sum + metric.value, 0);
    const totalErrors = recentErrors.reduce((sum, metric) => sum + metric.value, 0);
    const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

    const responseTimeValues = recentResponseTimes.map(metric => metric.value);
    const avgResponseTime = responseTimeValues.length > 0 
      ? responseTimeValues.reduce((sum, val) => sum + val, 0) / responseTimeValues.length 
      : 150; // Default if no data

    // Placeholder for percentile calculations - a more robust solution would sort and pick percentile
    const p95ResponseTime = responseTimeValues.length > 0 ? (Math.max(...responseTimeValues) * 0.95) : 200; // Simplified placeholder
    const p99ResponseTime = responseTimeValues.length > 0 ? (Math.max(...responseTimeValues) * 0.99) : 500; // Simplified placeholder

    return {
      requestsPerSecond: totalRequests / 300, // 5 minutes = 300 seconds
      avgResponseTime: avgResponseTime,
      errorRate,
      p95ResponseTime: p95ResponseTime,
      p99ResponseTime: p99ResponseTime,
      totalRequests,
      totalErrors
    };
  } catch (error) {
    console.error('Error getting API metrics:', error);
    // Return default metrics
    return {
      requestsPerSecond: 0,
      avgResponseTime: 150,
      errorRate: 0,
      p95ResponseTime: 200,
      p99ResponseTime: 500,
      totalRequests: 0,
      totalErrors: 0
    };
  }
}

async function getSystemMetrics() {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const uptime = process.uptime();

    // Calculate memory usage percentage (assuming 512MB container limit)
    const memoryLimit = parseInt(process.env.MEMORY_LIMIT || '536870912'); // 512MB default
    const memoryUsagePercent = memoryUsage.heapUsed / memoryLimit;

    // Simple CPU usage calculation (this is not perfectly accurate but gives an indication)
    const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000 / uptime * 100;

    return {
      memoryUsage: Math.min(memoryUsagePercent, 1), // Cap at 100%
      cpuUsage: Math.min(cpuPercent, 100), // Cap at 100%
      uptime,
      memoryDetails: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss
      },
      nodeVersion: process.version,
      platform: process.platform
    };
  } catch (error) {
    console.error('Error getting system metrics:', error);
    throw error;
  }
}

async function getActiveAlerts() {
  try {
    // Get recent performance alerts
    const alerts = [];

    // Check for performance issues and create alerts
    const dbStats = await getDatabaseMetrics().catch(() => null);
    const cacheStats = await getCacheMetrics().catch(() => null);
    const systemStats = await getSystemMetrics().catch(() => null);

    if (dbStats && dbStats.connections.active > 8) {
      alerts.push({
        id: 'high-db-connections',
        level: 'warning' as const,
        title: 'High Database Connections',
        message: `Database has ${dbStats.connections.active} active connections (threshold: 8)`,
        timestamp: new Date().toISOString()
      });
    }

    if (cacheStats && cacheStats.hitRatio < 0.8) {
      alerts.push({
        id: 'low-cache-hit-ratio',
        level: 'warning' as const,
        title: 'Low Cache Hit Ratio',
        message: `Cache hit ratio is ${(cacheStats.hitRatio * 100).toFixed(1)}% (threshold: 80%)`,
        timestamp: new Date().toISOString()
      });
    }

    if (systemStats && systemStats.memoryUsage > 0.9) {
      alerts.push({
        id: 'high-memory-usage',
        level: 'critical' as const,
        title: 'High Memory Usage',
        message: `System memory usage is ${(systemStats.memoryUsage * 100).toFixed(1)}% (threshold: 90%)`,
        timestamp: new Date().toISOString()
      });
    }

    if (!cacheStats?.redisAvailable) {
      alerts.push({
        id: 'redis-unavailable',
        level: 'warning' as const,
        title: 'Redis Unavailable',
        message: 'Redis cache is not available, using fallback cache',
        timestamp: new Date().toISOString()
      });
    }

    return alerts;
  } catch (error) {
    console.error('Error getting active alerts:', error);
    return [];
  }
}

async function recordApiMetrics(responseTime: number, status: 'success' | 'error') {
  try {
    await performanceMonitor.recordMetric('api.requests', 1, { endpoint: '/api/performance/metrics' });
    await performanceMonitor.recordMetric('api.response_time', responseTime, { endpoint: '/api/performance/metrics' });

    if (status === 'error') {
      await performanceMonitor.recordMetric('api.errors', 1, { endpoint: '/api/performance/metrics' });
    }
  } catch (error) {
    // Don't throw here as it would break the main response
    console.error('Error recording API metrics:', error);
  }
}

// Default metrics for fallback scenarios
function getDefaultDatabaseMetrics() {
  return {
    connections: { active: 0, idle: 0, total: 0, waiting: 0 },
    queryStats: { totalQueries: 0, avgDuration: 0, slowQueries: 0, errorRate: 0 },
    performance: { connectionTime: 0, queryTime: 0, throughput: 0 }
  };
}

function getDefaultCacheMetrics() {
  return {
    hitRatio: 0,
    memoryUsage: 0,
    operations: 0,
    redisAvailable: false,
    fallbackCacheSize: 0,
    performance: { avgResponseTime: 0, throughput: 0 }
  };
}

function getDefaultJobMetrics() {
  return {
    queued: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    delayed: 0,
    workers: 0,
    performance: { avgProcessingTime: 0, throughput: 0 }
  };
}

function getDefaultSystemMetrics() {
  return {
    memoryUsage: 0,
    cpuUsage: 0,
    uptime: 0,
    memoryDetails: { heapUsed: 0, heapTotal: 0, external: 0, rss: 0 },
    nodeVersion: process.version,
    platform: process.platform
  };
}
