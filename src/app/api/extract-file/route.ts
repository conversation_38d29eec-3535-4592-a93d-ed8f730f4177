import { NextRequest, NextResponse } from 'next/server';
import { extractContentFromFile } from '@/lib/services/content-extractor';

/**
 * API handler for extracting content from uploaded files
 *
 * We're adding special configuration to prevent timeouts and memory issues
 * with larger files, particularly PDFs that might be complex to process.
 */
export const maxDuration = 60; // 60 seconds maximum processing time
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb', // Allow larger file uploads (up to 25MB)
    },
    responseLimit: false, // No response size limit
  },
};

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'File is required' },
        { status: 400 }
      );
    }

    console.log(`Processing file: ${file.name}, Size: ${file.size} bytes, Type: ${file.type}`);

    try {
      // Extract content from file
      const content = await extractContentFromFile(file);

      // Return the extracted content
      console.log(`Successfully extracted content from ${file.name}`);
      return NextResponse.json({ content });
    } catch (extractionError: unknown) {
      console.error(`Specific extraction error for ${file.name}:`, extractionError);

      // More detailed error response
      const errorDetails = extractionError instanceof Error
        ? {
            message: extractionError.message,
            stack: process.env.NODE_ENV === 'development' ? extractionError.stack : undefined,
            name: extractionError.name
          }
        : { message: 'Unknown extraction error type' };

      return NextResponse.json(
        {
          error: 'Content extraction failed',
          details: errorDetails,
          fileInfo: {
            name: file.name,
            type: file.type,
            size: file.size
          }
        },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Error in API route:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to process request', message: errorMessage },
      { status: 500 }
    );
  }
}