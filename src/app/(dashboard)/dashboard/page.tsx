"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { WelcomeModal } from "@/components/onboarding/welcome-modal";
import { ActivityItemComponent } from "@/components/ui/activity-item";
import { EnhancedTipsResources } from "@/components/dashboard/enhanced-tips-resources";
import { getUserActivity, ActivityItem } from "@/lib/services/activity";

export default function DashboardPage() {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoadingActivity, setIsLoadingActivity] = useState(true);
  const [activityError, setActivityError] = useState<string | null>(null);

  // Load activity data on component mount
  useEffect(() => {
    const loadActivity = async () => {
      try {
        setIsLoadingActivity(true);
        setActivityError(null);
        const data = await getUserActivity(5); // Get last 5 activities
        setActivities(data);
      } catch (error) {
        console.error('Error loading activity:', error);
        setActivityError('Failed to load recent activity');
      } finally {
        setIsLoadingActivity(false);
      }
    };

    loadActivity();
  }, []);
  // Get current time to personalize greeting
  const currentHour = new Date().getHours();
  let greeting = "Welcome Back!";
  let greetingEmoji = "👋";

  if (currentHour < 12) {
    greeting = "Good Morning!";
    greetingEmoji = "☀️";
  } else if (currentHour < 18) {
    greeting = "Good Afternoon!";
    greetingEmoji = "🌤️";
  } else {
    greeting = "Good Evening!";
    greetingEmoji = "✨";
  }

  // For visual styling of the activity timeline
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });

  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } }
  };

  return (
    <>
      {/* Welcome modal for first-time users */}
      <WelcomeModal />

      <motion.div
        className="space-y-10 pb-10"
        initial="hidden"
        animate="show"
        variants={containerVariants}
      >
      {/* Enhanced welcome banner with subtle gradient and pattern */}
      <motion.div
        className="relative flex flex-col gap-2 p-8 rounded-xl bg-gradient-to-r from-primary/5 via-primary/15 to-primary/5 border border-primary/10 shadow-sm overflow-hidden"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-primary/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-primary/10 to-transparent blur-2xl"></div>

        <div className="flex items-center gap-4 relative z-10">
          <div className="rounded-full bg-primary/15 p-3 shadow-inner shadow-primary/5">
            <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              {greeting} <span className="inline-block animate-bounce duration-700">{greetingEmoji}</span>
            </h1>
            <p className="text-muted-foreground text-lg">
              Happy {today}! Create and manage your polls all in one place.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Main action cards - improved with subtle animations and better spacing */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        variants={containerVariants}
      >
        {/* Quick Poll Card */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden border-primary/15 hover:border-primary/40 transition-all duration-500 group hover:shadow-lg hover:shadow-primary/10">
            {/* Decorative corner accent */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-bl-full group-hover:bg-primary/15 transition-colors duration-500" />
            <div className="absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-primary/5 group-hover:bg-primary/10 transition-all duration-500" />

            <div className="p-8 relative">
              <div className="rounded-full w-14 h-14 bg-primary/10 flex items-center justify-center mb-5 group-hover:scale-110 transition-all duration-500 shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z" />
                </svg>
              </div>
              <div className="space-y-3">
                <h3 className="text-2xl font-medium flex items-center">Quick Poll <span className="ml-2">🚀</span></h3>
                <p className="text-muted-foreground mb-5 line-clamp-2">
                  Create a simple poll in seconds with AI-assisted options and templates.
                </p>
                <Button asChild size="lg" className="gap-2 group-hover:bg-primary/90 transition-all">
                  <Link href="/dashboard/create/conversational">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19" />
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                    Create New Poll
                  </Link>
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* My Analytics Card */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden border-blue-500/15 hover:border-blue-500/40 transition-all duration-500 group hover:shadow-lg hover:shadow-blue-500/10">
            {/* Decorative corner accent */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/5 rounded-bl-full group-hover:bg-blue-500/15 transition-colors duration-500" />
            <div className="absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-blue-500/5 group-hover:bg-blue-500/10 transition-all duration-500" />

            <div className="p-8 relative">
              <div className="rounded-full w-14 h-14 bg-blue-500/10 flex items-center justify-center mb-5 group-hover:scale-110 transition-all duration-500 shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                  <path d="M21.21 15.89A10 10 0 1 1 8 2.83" />
                  <path d="M22 12A10 10 0 0 0 12 2v10z" />
                </svg>
              </div>
              <div className="space-y-3">
                <h3 className="text-2xl font-medium flex items-center">My Analytics <span className="ml-2">📊</span></h3>
                <p className="text-muted-foreground mb-5 line-clamp-2">
                  Check the results and discover insights from your recent polls.
                </p>
                <Button asChild variant="outline" size="lg" className="gap-2 border-blue-500/20 text-blue-600 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-500/30 dark:text-blue-400 dark:hover:bg-blue-950 dark:hover:text-blue-300">
                  <Link href="/dashboard/analytics">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                      <line x1="8" y1="12" x2="16" y2="12" />
                      <line x1="12" y1="16" x2="12" y2="8" />
                    </svg>
                    View Results
                  </Link>
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* All Polls Card */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden border-violet-500/15 hover:border-violet-500/40 transition-all duration-500 group hover:shadow-lg hover:shadow-violet-500/10">
            {/* Decorative corner accent */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-violet-500/5 rounded-bl-full group-hover:bg-violet-500/15 transition-colors duration-500" />
            <div className="absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-violet-500/5 group-hover:bg-violet-500/10 transition-all duration-500" />

            <div className="p-8 relative">
              <div className="rounded-full w-14 h-14 bg-violet-500/10 flex items-center justify-center mb-5 group-hover:scale-110 transition-all duration-500 shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-violet-500">
                  <rect width="7" height="9" x="3" y="3" rx="1" />
                  <rect width="7" height="5" x="14" y="3" rx="1" />
                  <rect width="7" height="9" x="14" y="12" rx="1" />
                  <rect width="7" height="5" x="3" y="16" rx="1" />
                </svg>
              </div>
              <div className="space-y-3">
                <h3 className="text-2xl font-medium flex items-center">All Polls <span className="ml-2">📝</span></h3>
                <p className="text-muted-foreground mb-5 line-clamp-2">
                  Manage and edit your existing polls in one organized dashboard.
                </p>
                <Button asChild variant="outline" size="lg" className="gap-2 border-violet-500/20 text-violet-600 hover:bg-violet-50 hover:text-violet-700 hover:border-violet-500/30 dark:text-violet-400 dark:hover:bg-violet-950 dark:hover:text-violet-300">
                  <Link href="/dashboard/polls">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="8" y1="6" x2="21" y2="6" />
                      <line x1="8" y1="12" x2="21" y2="12" />
                      <line x1="8" y1="18" x2="21" y2="18" />
                      <line x1="3" y1="6" x2="3.01" y2="6" />
                      <line x1="3" y1="12" x2="3.01" y2="12" />
                      <line x1="3" y1="18" x2="3.01" y2="18" />
                    </svg>
                    View All Polls
                  </Link>
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>

      {/* Activity and Tips Section */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-8"
        variants={containerVariants}
      >
        {/* Activity Feed - improved with styled timeline */}
        <motion.div variants={itemVariants} className="col-span-1 md:col-span-2">
          <Card className="p-6 md:p-8 hover:shadow-md transition-shadow duration-300 border-muted/50">
            <h3 className="text-xl font-medium flex items-center gap-2 mb-4 pb-2 border-b">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <polyline points="22 12 16 12 14 15 10 15 8 12 2 12" />
                <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z" />
              </svg>
              Recent Activity
            </h3>

            <div className="space-y-1">
              {/* Loading state */}
              {isLoadingActivity && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-primary rounded-full animate-pulse"></div>
                    <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}

              {/* Error state */}
              {activityError && (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground mb-2">
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="12" />
                    <line x1="12" y1="16" x2="12.01" y2="16" />
                  </svg>
                  <p className="text-sm text-muted-foreground">{activityError}</p>
                </div>
              )}

              {/* Activity items */}
              {!isLoadingActivity && !activityError && activities.length > 0 && (
                activities.map((activity, index) => (
                  <ActivityItemComponent
                    key={activity.id}
                    activity={activity}
                    isLast={index === activities.length - 1}
                  />
                ))
              )}

              {/* Empty state */}
              {!isLoadingActivity && !activityError && activities.length === 0 && (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground mb-2">
                    <polyline points="22 12 16 12 14 15 10 15 8 12 2 12" />
                    <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z" />
                  </svg>
                  <p className="text-sm text-muted-foreground">No recent activity</p>
                  <p className="text-xs text-muted-foreground mt-1">Create your first poll to see activity here!</p>
                </div>
              )}
            </div>

            <Button variant="ghost" size="sm" className="mt-2 text-xs text-muted-foreground hover:text-foreground w-full">
              View all activity
            </Button>
          </Card>
        </motion.div>

        {/* Enhanced Tips & Resources with real data */}
        <motion.div variants={itemVariants}>
          <EnhancedTipsResources />
        </motion.div>
      </motion.div>
    </motion.div>
    </>
  );
}
