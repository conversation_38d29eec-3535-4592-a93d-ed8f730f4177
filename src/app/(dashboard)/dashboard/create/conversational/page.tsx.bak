'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { extractContentFromUrl } from '@/lib/services/content-extractor';
import { createPoll, PollQuestion, QuestionType, QuestionOption } from '@/lib/services/polls';
import { ConversationInterface, AIModel } from '@/components/conversation/ConversationInterface';
import { Loader2, ArrowLeft, Sparkles, Link as LinkIcon, Upload, Globe, CheckCircle2, FileText, Info } from 'lucide-react';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Too<PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from '@/components/ui/tooltip';

import { Message } from '@/components/conversation/ConversationInterface';

interface PollSuggestion {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'open' | 'text';
    options?: string[];
    required: boolean;
  }>;
}

export default function ConversationalCreatePage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for chat messages
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: "Hi there! I'm PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
    }
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [pollSuggestion, setPollSuggestion] = useState<PollSuggestion | null>(null);
  const [isCreatingPoll, setIsCreatingPoll] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [activeStep, setActiveStep] = useState<'chat' | 'review'>('chat');
  const [activeTab, setActiveTab] = useState<string>('chat');

  // Handle sending messages to different AI models
  const handleSendMessage = async (content: string, model: AIModel = 'perplexity') => {
    if (!content.trim()) return;

    // Add user message to chat
    const userMessage: Message = { role: 'user', content };
    setMessages(prev => [...prev, userMessage]);

    setIsLoading(true);

    try {
      // Choose API endpoint based on selected model
      const apiEndpoint = model === 'gemini' ? '/api/gemini' :
                         model === 'mistral' ? '/api/mistral' :
                         '/api/chat';

      // Call the appropriate API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage]
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      // Get the response text
      const responseText = await response.text();

      // Add AI response to chat
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: responseText
      }]);
    } catch (error) {
      console.error('Chat API error:', error);
      toast.error(`Failed to connect to the ${model} AI service. Please try again later.`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resource extraction
  const handleExtractResource = async (type: 'url' | 'website' | 'file', resource: string | File) => {
    if (!resource) return;

    setIsExtracting(true);

    // Show a toast to indicate the extraction has started
    const toastId = toast.loading(`Extracting content from ${type === 'url' ? 'URL' : type === 'website' ? 'website' : 'file'}...`);

    try {
      let content = '';

      if (type === 'url' && typeof resource === 'string') {
        // Show more detailed progress
        toast.loading(`Analyzing ${resource}...`, { id: toastId });

        // In production, use the extraction API to avoid serverless function limitations
        const isProduction = process.env.NODE_ENV === 'production' ||
                            process.env.NEXT_PUBLIC_VERCEL_ENV === 'production';

        try {
          if (isProduction) {
            // Use the API endpoint in production
            const extractionResponse = await fetch('/api/extract', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ url: resource }),
            });

            if (!extractionResponse.ok) {
              const errorData = await extractionResponse.json();
              throw new Error(errorData.error || `Extraction failed with status: ${extractionResponse.status}`);
            }

            const data = await extractionResponse.json();
            content = data.content;
          } else {
            // In development, use direct extraction
            content = await extractContentFromUrl(resource);
          }
        } catch (extractError) {
          console.error('Error using primary extraction method:', extractError);
          // Always fall back to direct extraction if the API approach fails
          content = await extractContentFromUrl(resource);
        }
      } else if (type === 'file' && resource instanceof File) {
        // Show more detailed progress for file extraction
        toast.loading(`Processing file: ${resource.name}...`, { id: toastId });
        console.log(`Starting extraction for file: ${resource.name}, type: ${resource.type}, size: ${Math.round(resource.size / 1024)}KB`);

        try {
          // Use the extract-file API for better error handling and timeout protection
          const formData = new FormData();
          formData.append('file', resource);

          // Add a longer timeout for file extraction (especially PDFs which can be complex)
          const extractionTimeout = 45000; // 45 seconds
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), extractionTimeout);

          toast.loading(`Extracting content with Mistral AI...`, { id: toastId });

          const extractionResponse = await fetch('/api/extract-file', {
            method: 'POST',
            body: formData,
            signal: controller.signal
          }).finally(() => clearTimeout(timeoutId));

          if (!extractionResponse.ok) {
            const errorData = await extractionResponse.json();
            console.error('File extraction error response:', errorData);
            throw new Error(errorData.error || `File extraction failed: ${extractionResponse.status}`);
          }

          const data = await extractionResponse.json();
          content = data.content;
          console.log(`Received extracted content (${content.length} chars)`);

          // Check if we have actual content - sometimes we might get empty or minimal responses
          if (!content || content.trim() === '' || content.length < 50) {
            console.warn('Extracted content was empty or too short');
            content = `Unable to extract meaningful content from ${resource.name}. Please try a different file format or enter your poll details manually.`;
          }
        } catch (fileError) {
          console.error('File extraction API error:', fileError);

          // Show a more detailed error to help with debugging
          const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error';
          toast.error(`File processing error: ${errorMessage}`, { id: toastId });

          // If this is specifically a Mistral API error, it might be related to the API key
          if (errorMessage.includes('MISTRAL_API_KEY') || errorMessage.includes('Mistral')) {
            toast.error(`The document extraction service is currently unavailable. Please try again later.`, { id: toastId });
          }

          // Provide a fallback content
          content = `File: ${resource.name} (Content extraction failed, please describe your poll manually)`;
        }
      }

      // If the content starts with an error message, handle it gracefully
      if (content.toLowerCase().includes('error') || content.toLowerCase().includes('failed to extract')) {
        toast.error(`We had trouble processing that content. Using what we could extract.`, { id: toastId });
      } else {
        toast.success(`Content extracted successfully!`, { id: toastId });
      }

      // Submit the extracted content to the chat
      const extractMessage = `I want to create a poll based on this content: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`;

      // Send the extracted content to the chat
      handleSendMessage(extractMessage);
      setActiveTab('chat');
    } catch (error) {
      console.error('Extraction error:', error);
      toast.error(`Failed to extract content: ${error instanceof Error ? error.message : 'Unknown error'}. Try a different URL or copy the content manually.`, { id: toastId });

      // Add a chat message with the error to guide the user
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: "I couldn't extract content from that source. You can try a different URL, or simply paste the content you want to use for your poll directly into our conversation."
      }]);
    } finally {
      setIsExtracting(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      try {
        handleExtractResource('file', e.target.files[0]);
      } catch (error) {
        console.error('File extraction error:', error);
        toast.error('Failed to process the file. Please try again.');
      }
    }
  };

  // Handle extraction from website
  const handleExtractFromWebsite = async () => {
    const url = prompt('Enter the website URL:');
    if (url) {
      try {
        handleExtractResource('url', url);
      } catch (error) {
        console.error('Website extraction error:', error);
        toast.error('Failed to extract content from the website. Please try again.');
      }
    }
  };

  // Handle extraction from file
  const handleExtractFromFile = () => {
    fileInputRef.current?.click();
  };

  // Check the last message for JSON poll data
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant') {
        try {
          // Look for JSON in the message
          const jsonMatch = lastMessage.content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const jsonContent = jsonMatch[0];
            const pollData = JSON.parse(jsonContent);

            // Validate that this is poll data
            if (pollData.title && pollData.description && Array.isArray(pollData.questions)) {
              setPollSuggestion(pollData);
              setActiveStep('review');
              toast.success('Poll draft created! Review and finalize your poll.');

              // We found a poll in the message
              console.log('Found poll data in message');
            }
          }
        } catch {
          // Not JSON or invalid JSON, that's fine
          console.log('No valid JSON found in message');
        }

        const data = await extractionResponse.json();
        content = data.content;
        console.log(`Received extracted content (${content.length} chars)`);

        // Check if we have actual content - sometimes we might get empty or minimal responses
        if (!content || content.trim() === '' || content.length < 50) {
          console.warn('Extracted content was empty or too short');
          content = `Unable to extract meaningful content from ${resource.name}. Please try a different file format or enter your poll details manually.`;
      const formattedQuestions: PollQuestion[] = pollSuggestion.questions.map((q, index) => {
        // Map the question type from the AI suggestion to our system
        let questionType: QuestionType = 'single';

        // Convert the question type
        if (q.type === 'single') questionType = 'single';
        else if (q.type === 'multiple') questionType = 'multiple';
        else if (q.type === 'open' || q.type === 'text') questionType = 'open';

        // Create question options
        const options: QuestionOption[] = q.options ? q.options.map((opt, i) => ({
          id: `o-${Date.now()}-${index}-${i}`,
          text: opt,
          value: opt.toLowerCase().replace(/\s+/g, '_')
        })) : [];

        return {
          id: `q-${Date.now()}-${index}`,
          text: q.text,
          type: questionType,
          options,
          required: q.required,
          order: index + 1
        };
      });

      // Create the poll
      const createdPoll = await createPoll({
        title: pollSuggestion.title,
        description: pollSuggestion.description,
        questions: formattedQuestions,
        userId: '', // This will be filled in by the server
        status: 'draft',
        expiresAt: null,
        is_public: true
      });

      // Show success toast
      toast.success('Poll created successfully!');

      // Navigate to the poll after a short delay
      setTimeout(() => {
        router.push(`/dashboard/polls/${createdPoll.id}`);
      }, 1500);
        })(),
        timeoutPromise
      ]);
    } catch (error) {
      console.error('Error creating poll:', error);
      
      // Enhanced error messaging based on error type
      let errorMessage = "I'm sorry, there was an error creating your poll. Please try again.";
      
      if (error instanceof Error) {
        // Check if it's a timeout error
        if (error.message.includes('timed out')) {
          errorMessage = "Poll creation timed out. The server might be busy. Please try again.";
        }
        // Check if it's a network error
        else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = "Network error. Please check your connection and try again.";
        }
        // Check if it's a server error
        else if (error.message.includes('500') || error.message.includes('server')) {
          errorMessage = "Server error. Our team has been notified. Please try again later.";
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsCreatingPoll(false);
    }
  };

  // Reset conversation and start over
  const handleResetConversation = () => {      // Clear messages and reset to initial state
    setMessages([
      {
        role: 'assistant',
        content: "Hi there! I&apos;m PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
      }
    ]);
    setPollSuggestion(null);
    setActiveStep('chat');
  };

  // Create a new poll from scratch
  const handleCreateFromScratch = () => {
    // Start over with a new poll
    handleResetConversation();
    // Send a message to start fresh
    handleSendMessage("I want to create a new poll from scratch");
  };

  // Example poll suggestion to try
  const handleTrySuggestion = () => {
    // Send a predefined message to get started with a customer satisfaction survey
    handleResetConversation();
    handleSendMessage("I want to create a customer satisfaction survey for my online store");
  };

  // Go back to chat from review
  const handleBackToChat = () => {
    setActiveStep('chat');
  };

  // Handle tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-6 pb-2">
        <div className="flex items-center gap-3">
          <div className="h-8 w-1 bg-primary rounded-full"></div>
          <h1 className="text-3xl font-bold">Create a Poll with AI</h1>
        </div>
        <Button asChild variant="outline" size="sm" className="gap-1">
          <Link href="/dashboard/create">
            <ArrowLeft className="h-4 w-4" />
            Back to Standard Creation
          </Link>
        </Button>
      </div>

      {/* Progress indicator */}
      <div className="px-6 pb-4">
        <div className="flex items-center justify-between max-w-md">
          <div className="flex flex-col items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${activeStep === 'chat' ? 'bg-primary text-white' : 'bg-primary text-white'}`}>
              1
            </div>
            <span className="text-xs mt-1">Describe Poll</span>
          </div>
          <div className={`h-0.5 flex-1 ${activeStep === 'chat' ? 'bg-muted' : 'bg-primary'}`}></div>
          <div className="flex flex-col items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${activeStep === 'review' ? 'bg-primary text-white' : 'bg-muted text-foreground'}`}>
              2
            </div>
            <span className="text-xs mt-1">Review & Create</span>
          </div>
        </div>
      </div>

      <div className="flex flex-1 p-6 pt-0">
        {activeStep === 'chat' ? (
          <div className="flex flex-col lg:flex-row gap-6 w-full">
            {/* Main area - Tabs for Chat and Import */}
            <div className="flex-1">
              <Tabs defaultValue="chat" value={activeTab} onValueChange={handleTabChange} className="h-full">
                <TabsList className="grid grid-cols-2 w-full">
                  <TabsTrigger value="chat">
                    <Sparkles className="h-4 w-4 mr-2" />
                    AI Chat
                  </TabsTrigger>
                  <TabsTrigger value="import">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Content
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="chat" className="mt-4 h-[calc(100%-48px)]">
                  <Card className="h-full flex flex-col overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">
                        <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                        Tell me about your poll
                      </CardTitle>
                      <CardDescription>
                        Describe what you want, and I&apos;ll help you create it.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 p-0 overflow-hidden">
                      <ConversationInterface
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        isLoading={isLoading}
                        placeholder="Describe your poll idea (e.g., 'I need a customer satisfaction survey')..."
                        className="flex-1"
                        showModelSelector={true}
                        defaultModel="perplexity"
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="import" className="mt-4">
                  <Card className="flex flex-col h-[500px]">
                    <CardHeader>
                      <CardTitle className="text-base">
                        <Upload className="h-4 w-4 inline mr-2 text-primary" />
                        Import Content for Your Poll
                      </CardTitle>
                      <CardDescription>
                        Import content from a URL, file or website to use as a base for your poll.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col justify-center items-center gap-6">
                      <div className="w-full max-w-md space-y-6">
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <LinkIcon className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from URL</h3>
                          </div>
                          <div className="flex space-x-2">
                            <Input
                              placeholder="Enter URL (e.g., blog post, article)"
                              className="flex-1"
                              onChange={(e) => setUrlInput(e.target.value)}
                              value={urlInput}
                              disabled={isExtracting}
                            />
                            <Button
                              variant="default"
                              onClick={() => handleExtractResource('url', urlInput)}
                              disabled={!urlInput || isExtracting}
                            >
                              {isExtracting ? <Loader2 className="h-4 w-4 animate-spin" /> : "Import"}
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Upload Document</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full h-24 border-dashed flex flex-col gap-2"
                            onClick={handleExtractFromFile}
                            disabled={isExtracting}
                          >
                            <Upload className="h-6 w-6" />
                            <span>
                              {isExtracting ? "Processing..." : "Click to upload a document"}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Supports .txt, .pdf, .docx files
                            </span>
                          </Button>
                          <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            className="hidden"
                            accept=".txt,.pdf,.docx,.doc"
                          />
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <Globe className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from Website</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={handleExtractFromWebsite}
                            disabled={isExtracting}
                          >
                            <Globe className="h-4 w-4 mr-2" />
                            {isExtracting ? "Processing website..." : "Extract from Website"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar with quick actions */}
            <div className="w-full lg:w-80 space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">
                    <Info className="h-4 w-4 inline mr-2 text-primary" />
                    How It Works
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">1</div>
                    <p>Describe your poll topic and audience in the chat, or import existing content as a starting point</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">2</div>
                    <p>The AI will help refine your idea and create a draft poll with appropriate questions</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">3</div>
                    <p>Review the generated poll and create it when you&apos;re ready</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">
                    <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full justify-start text-sm"
                    onClick={handleResetConversation}
                  >
                    Reset Conversation
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start text-sm"
                    onClick={handleCreateFromScratch}
                  >
                    <Sparkles className="h-3 w-3 mr-2" />
                    Create from Scratch
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start text-sm"
                    onClick={handleTrySuggestion}
                  >
                    <Sparkles className="h-3 w-3 mr-2" />
                    Try Example Poll
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">
                    <CheckCircle2 className="h-4 w-4 inline mr-2 text-primary" />
                    Poll Template Ideas
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I need a customer satisfaction survey")}>Customer Satisfaction</Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Create a poll to measure customer satisfaction</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I want to create an event feedback poll")}>Event Feedback</Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Create a poll to gather feedback about an event</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I need a product feedback survey")}>Product Feedback</Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Create a poll to gather feedback about a product</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("Help me create a team satisfaction survey")}>Team Satisfaction</Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Create a poll to measure team satisfaction</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <Card className="w-full">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <CheckCircle2 className="h-5 w-5 mr-2 text-green-500" />
                    Poll Draft Ready
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={handleBackToChat}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Chat
                  </Button>
                </div>
                <CardDescription>
                  Review your poll details below. You can go back to make changes or create the poll now.
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold">{pollSuggestion?.title}</h3>
                    <p className="text-muted-foreground mt-1">{pollSuggestion?.description}</p>
                  </div>

                  <div>
                    <h3 className="text-md font-semibold mb-2">Questions ({pollSuggestion?.questions.length})</h3>
                    <div className="space-y-4">
                      {pollSuggestion?.questions.map((question, index) => (
                        <div key={index} className="border rounded-md p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="font-medium">{question.text}</div>
                              <div className="flex gap-2 my-2">
                                <Badge variant="outline">{question.type}</Badge>
                                {question.required && <Badge>Required</Badge>}
                              </div>
                            </div>
                          </div>

                          {question.options && question.options.length > 0 && (
                            <div className="mt-2">
                              <div className="text-sm text-muted-foreground mb-1">Options:</div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {question.options.map((option, i) => (
                                  <div key={i} className="text-sm border rounded px-2 py-1">
                                    {option}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex justify-end gap-3">
                <Button variant="outline" onClick={handleBackToChat}>
                  Make Changes
                </Button>
                <Button
                  onClick={handleCreatePoll}
                  disabled={isCreatingPoll}
                  className="gap-2"
                >
                  {isCreatingPoll ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Creating Poll...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="h-4 w-4" />
                      Create Poll
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
