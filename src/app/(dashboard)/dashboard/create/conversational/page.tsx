'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { extractContentFromUrl } from '@/lib/services/content-extractor';
import { createPoll, PollQuestion, QuestionType, QuestionOption } from '@/lib/services/polls';
import { ConversationInterface, AIModel } from '@/components/conversation/ConversationInterface';
import { Loader2, ArrowLeft, Sparkles, Link as LinkIcon, Upload, Globe, CheckCircle2, FileText, Info, AlertCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { motion } from 'framer-motion';
import { extractPollDataFromMessage } from './json-detector';
import { withAuthRetry, ensureValidSession } from '@/lib/utils/session-manager';

import { Message } from '@/components/conversation/ConversationInterface';

// Note: The JSON code block styling has been moved to ConversationInterface.tsx
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const jsonCodeStyle = `
  .conversation pre code {
    white-space: pre-wrap !important;
    word-break: break-word !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
`;

interface PollSuggestion {
  title: string;
  description: string;
  questions: Array<{
    text: string;
    type: 'single' | 'multiple' | 'open' | 'text';
    options?: string[];
    required: boolean;
  }>;
}

export default function ConversationalCreatePage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load saved state from localStorage or use default initial values
  const getInitialMessages = (): Message[] => {
    if (typeof window === 'undefined') return [
      {
        role: 'assistant',
        content: "Hi there! I'm PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
      }
    ];

    const savedMessages = localStorage.getItem('pollgpt_conversation');
    return savedMessages ? JSON.parse(savedMessages) : [
      {
        role: 'assistant',
        content: "Hi there! I'm PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
      }
    ];
  };

  const getInitialPollSuggestion = (): PollSuggestion | null => {
    if (typeof window === 'undefined') return null;

    const savedPoll = localStorage.getItem('pollgpt_draft');
    return savedPoll ? JSON.parse(savedPoll) : null;
  };

  const getInitialStep = (): 'chat' | 'review' => {
    if (typeof window === 'undefined') return 'chat';
    return localStorage.getItem('pollgpt_step') === 'review' ? 'review' : 'chat';
  };

  // State for chat messages
  const [messages, setMessages] = useState<Message[]>(getInitialMessages());
  const [isLoading, setIsLoading] = useState(false);
  const [pollSuggestion, setPollSuggestion] = useState<PollSuggestion | null>(getInitialPollSuggestion());
  const [isCreatingPoll, setIsCreatingPoll] = useState(false);
  const isCreatingPollRef = useRef(false);

  // Ref to track timeout IDs for proper cleanup
  const timeoutRefsRef = useRef<{
    pollCreationTimeout: NodeJS.Timeout | null;
    pollCreationSafetyTimeout: NodeJS.Timeout | null;
  }>({
    pollCreationTimeout: null,
    pollCreationSafetyTimeout: null
  });

  // Keep the ref in sync with the state
  useEffect(() => {
    isCreatingPollRef.current = isCreatingPoll;
  }, [isCreatingPoll]);
  const [pollCreationCompleted, setPollCreationCompleted] = useState(false); // New state to track successful completion
  const [isExtracting, setIsExtracting] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [activeStep, setActiveStep] = useState<'chat' | 'review'>(getInitialStep());
  const [showRetryPrompt, setShowRetryPrompt] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('chat');

  // Handle sending messages to different AI models
  const handleSendMessage = async (content: string, model: AIModel = 'gemini') => {
    if (!content.trim()) return;

    // Add user message to chat
    const userMessage: Message = { role: 'user', content };
    setMessages(prev => [...prev, userMessage]);

    setIsLoading(true);

    try {
      // Choose API endpoint based on selected model
      const apiEndpoint = model === 'gemini' ? '/api/gemini' :
                         model === 'mistral' ? '/api/mistral' :
                         '/api/chat';

      // Call the appropriate API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          model: model
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      // Get the response data
      const contentType = response.headers.get('Content-Type') || '';
      console.log(`Response from ${apiEndpoint} - Content-Type:`, contentType);

      try {
        // Check for JSON response first (most APIs return JSON)
        if (contentType.includes('application/json')) {
          const jsonResponse = await response.json();

          // Extract the message content from the JSON response
          let responseText = '';

          // Handle different API response structures
          if (jsonResponse.choices && jsonResponse.choices[0] && jsonResponse.choices[0].message) {
            // OpenAI/Perplexity style response
            responseText = jsonResponse.choices[0].message.content;
          } else if (jsonResponse.content) {
            // Simple content field
            responseText = jsonResponse.content;
          } else {
            // Fallback - use stringify
            responseText = JSON.stringify(jsonResponse);
          }

          // Add AI response to chat
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: responseText
          }]);
        }
        // Handle text/plain responses
        else if (contentType.includes('text/plain')) {
          const responseText = await response.text();

          // Add AI response to chat
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: responseText
          }]);
        }
        // Handle streaming responses
        else if (response.body && (contentType.includes('text/event-stream') || contentType === '')) {
          // Initialize an empty response
          setMessages(prev => [...prev, { role: 'assistant', content: '' }]);

          // Get the reader from the response body
          const reader = response.body.getReader();

          // Set up the text decoder
          const decoder = new TextDecoder();
          let fullContent = '';

          // Read the chunks of data as they come in
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            // Decode the chunk and add it to the message content
            const chunk = decoder.decode(value, { stream: true });
            fullContent += chunk;

            try {
              // Try to parse as SSE
              // Extract data strings from SSE format (data: {...}\n\n)
              const dataStrings = fullContent.split('\n\n')
                .filter(line => line.trim().startsWith('data: '))
                .map(line => line.replace(/^data: /, '').trim());

              if (dataStrings.length > 0) {
                // Process the last data string
                const lastData = dataStrings[dataStrings.length - 1];

                try {
                  // Try to parse as JSON
                  const parsedData = JSON.parse(lastData);
                  const content = parsedData.choices?.[0]?.delta?.content ||
                                  parsedData.choices?.[0]?.message?.content ||
                                  parsedData.content || '';

                  if (content) {
                    // Update with parsed content
                    setMessages(prev => {
                      const newMessages = [...prev];
                      newMessages[newMessages.length - 1] = {
                        ...newMessages[newMessages.length - 1],
                        content: content
                      };
                      return newMessages;
                    });
                  }
                } catch {
                  // Not valid JSON, use as is
                  setMessages(prev => {
                    const newMessages = [...prev];
                    newMessages[newMessages.length - 1] = {
                      ...newMessages[newMessages.length - 1],
                      content: lastData
                    };
                    return newMessages;
                  });
                }
              } else {
                // No SSE data found, update with raw content
                setMessages(prev => {
                  const newMessages = [...prev];
                  newMessages[newMessages.length - 1] = {
                    ...newMessages[newMessages.length - 1],
                    content: fullContent
                  };
                  return newMessages;
                });
              }
            } catch {
              // If parsing fails, just use the raw text
              setMessages(prev => {
                const newMessages = [...prev];
                newMessages[newMessages.length - 1] = {
                  ...newMessages[newMessages.length - 1],
                  content: fullContent
                };
                return newMessages;
              });
            }
          }
        } else {
          // Fallback for any other content types
          const responseText = await response.text();
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: responseText
          }]);
        }
      } catch (error) {
        console.error('Error processing API response:', error);

        // Fallback to reading as text if JSON parsing fails
        try {
          const fallbackText = await response.clone().text();
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: fallbackText
          }]);
        } catch {
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: `Error processing response: ${error.message}`
          }]);
        }
      }
    } catch (error) {
      console.error('Chat API error:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to connect to the ${model} AI service: ${errorMsg}. Please try again later.`);

      // Add a failure message to the chat
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: `I'm sorry, there was an error connecting to the ${model} AI service. Please try again or select a different AI model.`
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resource extraction
  const handleExtractResource = async (type: 'url' | 'website' | 'file', resource: string | File) => {
    if (!resource) return;

    setIsExtracting(true);

    // Show a toast to indicate the extraction has started
    const toastId = toast.loading(`Extracting content from ${type === 'url' ? 'URL' : type === 'website' ? 'website' : 'file'}...`);

    try {
      let content = '';

      if (type === 'url' && typeof resource === 'string') {
        // Show more detailed progress
        toast.loading(`Analyzing ${resource}...`, { id: toastId });

        // In production, use the extraction API to avoid serverless function limitations
        const isProduction = process.env.NODE_ENV === 'production' ||
                            process.env.NEXT_PUBLIC_VERCEL_ENV === 'production';

        try {
          if (isProduction) {
            // Use the API endpoint in production
            const extractionResponse = await fetch('/api/extract', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ url: resource }),
            });

            if (!extractionResponse.ok) {
              const errorData = await extractionResponse.json();
              throw new Error(errorData.error || `Extraction failed with status: ${extractionResponse.status}`);
            }

            const data = await extractionResponse.json();
            content = data.content;
          } else {
            // In development, use direct extraction
            content = await extractContentFromUrl(resource);
          }
        } catch (extractError) {
          console.error('Error using primary extraction method:', extractError);
          // Always fall back to direct extraction if the API approach fails
          content = await extractContentFromUrl(resource);
        }
      } else if (type === 'file' && resource instanceof File) {
        // Show more detailed progress for file extraction
        toast.loading(`Processing file: ${resource.name}...`, { id: toastId });
        console.log(`Starting extraction for file: ${resource.name}, type: ${resource.type}, size: ${Math.round(resource.size / 1024)}KB`);

        try {
          // Use the extract-file API for better error handling and timeout protection
          const formData = new FormData();
          formData.append('file', resource);

          // Add a longer timeout for file extraction (especially PDFs which can be complex)
          const extractionTimeout = 45000; // 45 seconds
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), extractionTimeout);

          toast.loading(`Extracting content with Mistral AI...`, { id: toastId });

          const extractionResponse = await fetch('/api/extract-file', {
            method: 'POST',
            body: formData,
            signal: controller.signal
          }).finally(() => clearTimeout(timeoutId));

          if (!extractionResponse.ok) {
            const errorData = await extractionResponse.json();
            console.error('File extraction error response:', errorData);
            throw new Error(errorData.error || `File extraction failed: ${extractionResponse.status}`);
          }

          const data = await extractionResponse.json();
          content = data.content;
          console.log(`Received extracted content (${content.length} chars)`);

          // Check if we have actual content - sometimes we might get empty or minimal responses
          if (!content || content.trim() === '' || content.length < 50) {
            console.warn('Extracted content was empty or too short');
            content = `Unable to extract meaningful content from ${resource.name}. Please try a different file format or enter your poll details manually.`;
          }
        } catch (fileError) {
          console.error('File extraction API error:', fileError);

          // Show a more detailed error to help with debugging
          const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error';
          toast.error(`File processing error: ${errorMessage}`, { id: toastId });

          // If this is specifically a Mistral API error, it might be related to the API key
          if (errorMessage.includes('MISTRAL_API_KEY') || errorMessage.includes('Mistral')) {
            toast.error(`The document extraction service is currently unavailable. Please try again later.`, { id: toastId });
          }

          // Provide a fallback content
          content = `File: ${resource.name} (Content extraction failed, please describe your poll manually)`;
        }
      }

      // If the content starts with an error message, handle it gracefully
      if (content.toLowerCase().includes('error') || content.toLowerCase().includes('failed to extract')) {
        toast.error(`We had trouble processing that content. Using what we could extract.`, { id: toastId });
      } else {
        toast.success(`Content extracted successfully!`, { id: toastId });
      }

      // Submit the extracted content to the chat
      const extractMessage = `I want to create a poll based on this content: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`;

      // Send the extracted content to the chat
      handleSendMessage(extractMessage);
      setActiveTab('chat');
    } catch (error) {
      console.error('Extraction error:', error);
      toast.error(`Failed to extract content: ${error instanceof Error ? error.message : 'Unknown error'}. Try a different URL or copy the content manually.`, { id: toastId });

      // Add a chat message with the error to guide the user
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: "I couldn't extract content from that source. You can try a different URL, or simply paste the content you want to use for your poll directly into our conversation."
      }]);
    } finally {
      setIsExtracting(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      try {
        handleExtractResource('file', e.target.files[0]);
      } catch (error) {
        console.error('File extraction error:', error);
        toast.error('Failed to process the file. Please try again.');
      }
    }
  };

  // Handle extraction from website
  const handleExtractFromWebsite = async () => {
    const url = prompt('Enter the website URL:');
    if (url) {
      try {
        handleExtractResource('url', url);
      } catch (error) {
        console.error('Website extraction error:', error);
        toast.error('Failed to extract content from the website. Please try again.');
      }
    }
  };

  // Handle extraction from file
  const handleExtractFromFile = () => {
    fileInputRef.current?.click();
  };

  // Create the poll in the database
  const handleCreatePoll = useCallback(async () => {
    if (!pollSuggestion) return;

    // Don't do anything if we're already creating a poll
    if (isCreatingPollRef.current) return;

    setIsCreatingPoll(true);
    setShowRetryPrompt(false);

    // Create a unique toast ID to track and update the toast
    const toastId = toast.loading('Creating your poll...');

    // Set up a timer to show retry prompt if operation takes too long
    const timeoutDelay = 15000; // 15 seconds
    const timeoutId = setTimeout(() => {
      // Only show retry prompt if we're still creating the poll
      if (isCreatingPollRef.current) {
        setShowRetryPrompt(true);
        toast.loading('Still creating your poll... This is taking longer than usual', { id: toastId });
      }
    }, timeoutDelay);

    // Update the shared timeout refs so they can be cleaned up if the component unmounts
    if (timeoutRefsRef.current) {
      timeoutRefsRef.current.pollCreationTimeout = timeoutId;
    }

    // Set up a safety timeout to prevent infinite loading state
    const safetyTimeoutId = setTimeout(() => {
      if (isCreatingPollRef.current) {
        console.warn('Poll creation safety timeout triggered - forcing loading state reset');
        setIsCreatingPoll(false);
        toast.error("Poll creation timed out. Please try again.", { id: toastId });
      }
    }, 45000); // 45 seconds absolute maximum

    // Update safety timeout ref
    if (timeoutRefsRef.current) {
      timeoutRefsRef.current.pollCreationSafetyTimeout = safetyTimeoutId;
    }

    // Make sure we clean up timeouts if the operation completes
    const cleanupTimeouts = () => {
      clearTimeout(timeoutId);
      clearTimeout(safetyTimeoutId);

      // Also clear the refs
      if (timeoutRefsRef.current) {
        timeoutRefsRef.current.pollCreationTimeout = null;
        timeoutRefsRef.current.pollCreationSafetyTimeout = null;
      }
    };

    try {
      // Ensure the session is valid before attempting to create a poll
      // Pass options to prevent automatic redirection and use custom error handling
      const sessionValid = await ensureValidSession({
        showToasts: false,
        redirectOnFailure: false,
        forceRefresh: true // Ensure we have a fresh session
      });

      if (!sessionValid) {
        console.warn('Session invalid before poll creation');
        throw new Error('Authentication session invalid. Please log in again.');
      }

      // Format questions for the database
      const formattedQuestions: PollQuestion[] = pollSuggestion.questions.map((q, index) => {
        // Map the question type from the AI suggestion to our system
        let questionType: QuestionType = 'single';

        // Convert the question type
        if (q.type === 'single') questionType = 'single';
        else if (q.type === 'multiple') questionType = 'multiple';
        else if (q.type === 'open' || q.type === 'text') questionType = 'open';

        // Create question options with timestamp to ensure uniqueness
        const timestamp = Date.now() + index;
        const options: QuestionOption[] = q.options ? q.options.map((opt, i) => ({
          id: `o-${timestamp}-${i}`,
          text: opt,
          value: opt.toLowerCase().replace(/\\s+/g, '_')
        })) : [];

        return {
          id: `q-${timestamp}-${index}`,
          text: q.text,
          type: questionType,
          options,
          required: q.required !== undefined ? q.required : true,
          order: index + 1
        };
      });

      console.log('Creating poll with formatted questions:', formattedQuestions);

      // Update toast to show we're connecting to the database
      toast.loading('Connecting to database...', { id: toastId });

      // Create the poll with automatic auth retry on failure - increase retries to 3
      const createdPoll = await withAuthRetry(async () => {
        return await createPoll({
          title: pollSuggestion.title,
          description: pollSuggestion.description,
          questions: formattedQuestions,
          userId: '', // This will be filled in by the server
          status: 'draft', // Ensure polls are created as drafts
          expiresAt: null,
          is_public: true
        });
      }, 3); // Allow up to 3 retries for auth issues

      // Clear both timeouts since the operation completed successfully
      cleanupTimeouts();

      // Update the loading toast to success
      toast.success('Poll created successfully!', { id: toastId });

      // Mark creation as completed first
      setPollCreationCompleted(true);
      setShowRetryPrompt(false);

      // Clear localStorage since the poll has been created successfully
      localStorage.removeItem('pollgpt_conversation');
      localStorage.removeItem('pollgpt_draft');
      localStorage.removeItem('pollgpt_step');
      localStorage.removeItem('pollgpt_retry_after_refresh');

      // Set creating state to false before navigation
      setIsCreatingPoll(false);

      // Force navigation with a hard redirect to ensure we get to the poll page
      // This is more reliable than router.push which might get interrupted
      console.log(`Redirecting to poll page: /dashboard/polls/${createdPoll.id}`);

      // Use window.location for a hard redirect that can't be interrupted
      window.location.href = `/dashboard/polls/${createdPoll.id}?newPoll=true`;

      // As a fallback, also try router.push with a longer delay
      setTimeout(() => {
        if (document.location.pathname.includes('/create/conversational')) {
          console.log('Fallback navigation with router.push');
          router.push(`/dashboard/polls/${createdPoll.id}?newPoll=true`);
        }
      }, 1000);

      // Return early to avoid reaching the finally block too soon after successful navigation
      return;
    } catch (error) {
      console.error('Error creating poll:', error);

      // Clear the timeouts since the operation completed (with an error)
      cleanupTimeouts();

      // Get a more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('Poll creation error details:', { errorMessage, pollData: pollSuggestion });

      // Show appropriate error message based on error type
      if (errorMessage.toLowerCase().includes('authenticated') ||
          errorMessage.toLowerCase().includes('auth') ||
          errorMessage.toLowerCase().includes('session') ||
          errorMessage.toLowerCase().includes('login')) {

        toast.error("Authentication error. Please log in again and try creating your poll.", { id: toastId });

        // Store retry info but don't redirect - wait for the user to refresh
        localStorage.setItem('pollgpt_retry_after_refresh', 'true');

        // Set a timeout to reload the page automatically after a short delay
        setTimeout(() => {
          if (window.confirm('Your session has expired. The page will reload to refresh your session. Is that OK?')) {
            window.location.reload();
          }
        }, 2000);
      } else {
        toast.error(`Failed to create poll: ${errorMessage}. Please try refreshing the page and trying again.`, { id: toastId });
      }
    } finally {
      // Ensure loading state is cleared if not already handled by successful navigation path
      // This will run if an error occurs or if the return statement isn't hit.
      if (isCreatingPollRef.current) { // Check ref to see if it was set to true
         setIsCreatingPoll(false);
      }
      setShowRetryPrompt(false);
    }
  }, [pollSuggestion, router]);
  // Add this effect to handle cleanup of poll creation timeouts
  useEffect(() => {
    // Capture the current value of the ref when the effect runs
    const currentTimeoutRefs = timeoutRefsRef.current;

    // Return cleanup function that will be called when component unmounts
    return () => {
      // Clean up any timeouts using the captured value
      if (currentTimeoutRefs?.pollCreationTimeout) {
        clearTimeout(currentTimeoutRefs.pollCreationTimeout);
      }
      if (currentTimeoutRefs?.pollCreationSafetyTimeout) {
        clearTimeout(currentTimeoutRefs.pollCreationSafetyTimeout);
      }
    };
  }, []);

  useEffect(() => {
    if (messages.length > 0) {
      // Save messages to localStorage whenever they change
      localStorage.setItem('pollgpt_conversation', JSON.stringify(messages));

      const lastMessage = messages[messages.length - 1];
      // Skip system messages and skip if processing is in progress
      if (lastMessage.role === 'assistant' && !lastMessage.processed) {
        console.log("Checking for JSON in message:", lastMessage.content.substring(0, 50) + "...");

        try {
          // Use the helper function to extract poll data
          const extractedData = extractPollDataFromMessage(lastMessage.content);

          if (extractedData && extractedData.data) {
            // We found valid poll data
            const pollData = extractedData.data;
            // We've found valid poll data, now we'll create a user-friendly message
          // and skip the JSON formatting since we're not displaying it anymore

          // Extract only the important parts of the message before the JSON
          // This keeps any useful context from the AI but removes the JSON
          const messageParts = lastMessage.content.split('```');
          const cleanedContent = messageParts[0]; // Get the text before the first code block

          // Create a more user-friendly message that doesn't show the raw JSON
          const updatedContent = `${cleanedContent.trim()}

I've created a draft poll based on our conversation! You can review and edit it now.

✅ Title: "${pollData.title}"
✅ Description: ${pollData.description}
✅ Questions: ${pollData.questions.length} questions created

You can now review and edit this draft poll.`;

          // Flag to prevent re-processing this message
          const processedMessage: Message = {
            role: 'assistant',
            content: updatedContent,
            processed: true
          };

          // Update the message with the user-friendly content instead of JSON
          setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = processedMessage;
            return updated;
          });

          // Set the poll suggestion and change to the review step
          setPollSuggestion(pollData);
          setActiveStep('review');
          toast.success('Poll draft created! Review and finalize your poll.');

          // Log that we found poll data
          console.log('Found poll data in message');
        } else {
          // No valid poll data found
          console.log('No valid JSON poll data found in message');

          // Mark the message as processed to avoid checking it again
          setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = {
              ...updated[updated.length - 1],
              processed: true
            };
            return updated;
          });
        }
        } catch (error) {
          console.error('Error processing JSON in message:', error);

          // Mark the message as processed to avoid checking it again
          setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = {
              ...updated[updated.length - 1],
              processed: true
            };
            return updated;
          });
        }
      }
    }
  }, [messages]);

  // Save poll suggestion and active step to localStorage whenever they change
  useEffect(() => {
    if (pollSuggestion) {
      localStorage.setItem('pollgpt_draft', JSON.stringify(pollSuggestion));
    }
    localStorage.setItem('pollgpt_step', activeStep);
  }, [pollSuggestion, activeStep]);

  // Show a notification if we restored data from localStorage
  // Also handle retry logic after refresh
  useEffect(() => {
    const hadSavedData = localStorage.getItem('pollgpt_conversation') || localStorage.getItem('pollgpt_draft');
    const shouldRetryAfterRefresh = localStorage.getItem('pollgpt_retry_after_refresh') === 'true';

    // Only show this once when the component first loads and if we had data to restore
    if (hadSavedData && typeof window !== 'undefined') {
      // Use a flag in session storage to ensure this only shows once per session
      const hasShownRestoreMessage = sessionStorage.getItem('pollgpt_restore_message_shown');

      if (!hasShownRestoreMessage) {
        toast.success('Your previous work has been restored!', {
          description: 'Your conversation and draft poll data were recovered.',
          duration: 4000
        });
        sessionStorage.setItem('pollgpt_restore_message_shown', 'true');
      }

      // If we're supposed to retry creating the poll after refresh
      if (shouldRetryAfterRefresh && pollSuggestion && activeStep === 'review') {
        // Clear the retry flag
        localStorage.removeItem('pollgpt_retry_after_refresh');

        // First check if the session is valid before retrying
        const checkSessionAndRetry = async () => {
          try {
            // Show a notification that we're checking authentication
            const toastId = toast.loading('Validating your session before retrying poll creation...');

            // Validate the session without redirecting or showing toasts
            const isValid = await ensureValidSession({
              showToasts: false,
              redirectOnFailure: false
            });

            if (isValid) {
              // Session is valid, now retry poll creation
              toast.success('Session validated, retrying poll creation...', { id: toastId });

              // Wait a moment before retrying to ensure the page is fully loaded
              setTimeout(() => {
                // Make sure we're not already creating a poll
                if (!isCreatingPollRef.current) {
                  handleCreatePoll();
                }
              }, 1000);
            } else {
              // Session is invalid, show user a message with option to login
              toast.error('Your session has expired. Please log in again before creating your poll.', { id: toastId });

              // Ask user if they want to go to login page
              setTimeout(() => {
                if (window.confirm('Would you like to log in again to continue creating your poll?')) {
                  // Save current path to return to after login
                  sessionStorage.setItem('pollgpt_return_path', window.location.pathname);
                  window.location.href = '/login';
                }
              }, 1000);
            }
          } catch (error) {
            console.error('Error checking session before poll creation retry:', error);
            toast.error('Failed to validate your session. Please try creating your poll again manually.');
            setIsCreatingPoll(false);
          }
        };

        // Start the session check and retry process
        checkSessionAndRetry();
      }
    }
  }, [pollSuggestion, activeStep, handleCreatePoll, isCreatingPoll]);



  // Reset conversation and start over
  const handleResetConversation = () => {      // Clear messages and reset to initial state
    const initialMessage: Message = {
      role: 'assistant' as const,
      content: "Hi there! I&apos;m PollGPT. Tell me about the poll you want to create. What topic are you interested in?"
    };

    // Update state
    setMessages([initialMessage]);
    setPollSuggestion(null);
    setActiveStep('chat');

    // Clear localStorage
    localStorage.setItem('pollgpt_conversation', JSON.stringify([initialMessage]));
    localStorage.removeItem('pollgpt_draft');
    localStorage.setItem('pollgpt_step', 'chat');
  };

  // Discard draft poll and return to chat
  const handleDiscardDraft = () => {
    // Show confirmation toast
    toast.success('Draft poll discarded');

    // Find and remove any messages containing JSON data or code blocks with poll data
    const cleanedMessages = messages.filter(message => {
      // Check if the message content contains a JSON object with poll data
      if (message.role === 'assistant' && message.content) {
        // First check for code blocks with JSON
        if (message.content.includes('```json')) {
          try {
            // Extract JSON from code block
            const codeBlockMatch = message.content.match(/```json\n([\s\S]*?)\n```/);
            if (codeBlockMatch && codeBlockMatch[1]) {
              const possiblePollData = JSON.parse(codeBlockMatch[1]);
              // If this looks like poll data, remove it
              if (possiblePollData.title && possiblePollData.description && Array.isArray(possiblePollData.questions)) {
                return false; // Filter out this message
              }
            }
          } catch {
            // Not valid JSON in code block, keep checking
          }
        }

        // Use the helper function as a backup check
        const extractResult = extractPollDataFromMessage(message.content);
        if (extractResult && extractResult.data) {
          return false; // Filter out this message if it contains poll data
        }
      }
      return true; // Keep all other messages
    });

    // Add a message explaining what happened
    const confirmationMessage: Message = {
      role: 'assistant',
      content: "I've discarded the draft poll. Let me know if you'd like to start a new poll or modify your previous idea."
    };

    // Update state with cleaned messages and add confirmation
    setMessages([...cleanedMessages, confirmationMessage]);
    setPollSuggestion(null);
    setActiveStep('chat');

    // Update localStorage
    localStorage.setItem('pollgpt_conversation', JSON.stringify([...cleanedMessages, confirmationMessage]));
    localStorage.removeItem('pollgpt_draft');
    localStorage.setItem('pollgpt_step', 'chat');
  };

  // Create a new poll from scratch
  const handleCreateFromScratch = () => {
    // Start over with a new poll
    handleResetConversation();
    // Send a message to start fresh
    handleSendMessage("I want to create a new poll from scratch");
  };

  // Example poll suggestion to try
  const handleTrySuggestion = () => {
    // Send a predefined message to get started with a customer satisfaction survey
    handleResetConversation();
    handleSendMessage("I want to create a customer satisfaction survey for my online store");
  };

  // Go back to chat from review
  const handleBackToChat = () => {
    // Add a message asking what changes the user would like to make
    setMessages(prev => [...prev, {
      role: 'assistant',
      content: "What changes would you like to make to the poll draft? Let me know, and I'll help you refine it."
    }]);

    setActiveStep('chat');
  };

  // Handle tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle refreshing the page and retrying poll creation
  const handleRefreshAndRetry = () => {
    // Store information that we want to retry after refresh
    localStorage.setItem('pollgpt_retry_after_refresh', 'true');

    // Reset loading states before refresh to ensure a clean state
    setIsCreatingPoll(false);
    setShowRetryPrompt(false);

    // Add a small delay to ensure state changes are applied before refresh
    setTimeout(() => {
      // Refresh the page
      window.location.reload();
    }, 100);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } }
  };

  return (
    <motion.div
      className="space-y-8 pb-10"
      initial="hidden"
      animate="show"
      variants={containerVariants}
    >
      {/* Beautiful hero header section with gradient background */}
      <motion.div
        className="relative flex flex-col gap-4 p-8 rounded-xl bg-gradient-to-r from-blue-500/5 via-blue-500/10 to-blue-500/5 border border-blue-500/10 shadow-sm overflow-hidden mb-8"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-blue-500/10 to-transparent blur-2xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              Create a Poll with AI
              {/* <span className="text-blue-500 ml-1">🤖</span> */}
            </h1>
            <p className="text-muted-foreground mt-1">
              Describe your poll idea and let AI create it for you
            </p>
          </div>
          <Button asChild variant="outline" size="sm" className="gap-1 w-full sm:w-auto shadow-sm hover:shadow-md transition-shadow">
            <Link href="/dashboard/create">
              Try Standard Creation
            </Link>
          </Button>
        </div>
      </motion.div>

      <div className="container mx-auto px-4">
        {/* Progress indicator */}
        <motion.div
          className="pb-4 mx-auto"
          variants={itemVariants}
        >
        <div className="flex items-center justify-between max-w-xs sm:max-w-md mx-auto">
          <div className="flex flex-col items-center">
            <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm ${activeStep === 'chat' ? 'bg-primary text-white' : 'bg-primary text-white'}`}>
              1
            </div>
            <span className="text-xs mt-1 text-center">Describe Poll</span>
          </div>
          <div className={`h-0.5 flex-1 mx-2 sm:mx-4 ${activeStep === 'chat' ? 'bg-muted' : 'bg-primary'}`}></div>
          <div className="flex flex-col items-center">
            <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm ${activeStep === 'review' ? 'bg-primary text-white' : 'bg-muted text-foreground'}`}>
              2
            </div>
            <span className="text-xs mt-1 text-center">Review & Create</span>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="flex flex-1 p-0 pt-0"
        variants={itemVariants}
      >
        {activeStep === 'chat' ? (
          <div className="flex flex-col gap-6 w-full">
            {/* Main area - Tabs for Chat and Import - Full width */}
            <div className="w-full">
              <Tabs defaultValue="chat" value={activeTab} onValueChange={handleTabChange} className="h-full">
                <TabsList className="grid grid-cols-2 w-full">
                  <TabsTrigger value="chat">
                    <Sparkles className="h-4 w-4 mr-2" />
                    AI Chat
                  </TabsTrigger>
                  <TabsTrigger value="import">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Content
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="chat" className="mt-4 h-[60vh] sm:h-[calc(100%-48px)]">
                  <Card className="h-full flex flex-col overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">
                        <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                        Tell me about your poll
                      </CardTitle>
                      <CardDescription>
                        Describe what you want, and I&apos;ll help you create it.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 p-0 overflow-hidden">
                      <ConversationInterface
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        isLoading={isLoading}
                        placeholder="Describe your poll idea (e.g., 'I need a customer satisfaction survey')..."
                        className="flex-1"
                        showModelSelector={true}
                        defaultModel="gemini"
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="import" className="mt-4">
                  <Card className="flex flex-col h-[500px]">
                    <CardHeader>
                      <CardTitle className="text-base">
                        <Upload className="h-4 w-4 inline mr-2 text-primary" />
                        Import Content for Your Poll
                      </CardTitle>
                      <CardDescription>
                        Import content from a URL, file or website to use as a base for your poll.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col justify-center items-center gap-6">
                      <div className="w-full max-w-md space-y-6">
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <LinkIcon className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from URL</h3>
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Input
                              placeholder="Enter URL (e.g., blog post, article)"
                              className="flex-1"
                              onChange={(e) => setUrlInput(e.target.value)}
                              value={urlInput}
                              disabled={isExtracting}
                            />
                            <Button
                              variant="default"
                              onClick={() => handleExtractResource('url', urlInput)}
                              disabled={!urlInput || isExtracting}
                              className="w-full sm:w-auto"
                            >
                              {isExtracting ? <Loader2 className="h-4 w-4 animate-spin" /> : "Import"}
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Upload Document</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full h-24 border-dashed flex flex-col gap-2"
                            onClick={handleExtractFromFile}
                            disabled={isExtracting}
                          >
                            <Upload className="h-6 w-6" />
                            <span>
                              {isExtracting ? "Processing..." : "Click to upload a document"}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Supports .txt, .pdf, .docx files
                            </span>
                          </Button>
                          <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            className="hidden"
                            accept=".txt,.pdf,.docx,.doc"
                          />
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <Globe className="h-5 w-5 mr-2 text-primary" />
                            <h3 className="font-medium">Import from Website</h3>
                          </div>
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={handleExtractFromWebsite}
                            disabled={isExtracting}
                          >
                            <Globe className="h-4 w-4 mr-2" />
                            {isExtracting ? "Processing website..." : "Extract from Website"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Card row below chat - Responsive cards layout */}
            <div className="w-full">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <Info className="h-4 w-4 inline mr-2 text-primary" />
                      How It Works
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">1</div>
                      <p>Describe your poll topic and audience in the chat, or import existing content as a starting point</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">2</div>
                      <p>The AI will help refine your idea and create a draft poll with appropriate questions</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs shrink-0">3</div>
                      <p>Review the generated poll and create it when you&apos;re ready</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <Sparkles className="h-4 w-4 inline mr-2 text-primary" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleResetConversation}
                    >
                      Reset Conversation
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleCreateFromScratch}
                    >
                      <Sparkles className="h-3 w-3 mr-2" />
                      Create from Scratch
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full justify-start text-sm"
                      onClick={handleTrySuggestion}
                    >
                      <Sparkles className="h-3 w-3 mr-2" />
                      Try Example Poll
                    </Button>
                  </CardContent>
                </Card>

                <Card className="h-full">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      <CheckCircle2 className="h-4 w-4 inline mr-2 text-primary" />
                      Poll Template Ideas
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I need a customer satisfaction survey")}>Customer Satisfaction</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to measure customer satisfaction</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I want to create an event feedback poll")}>Event Feedback</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to gather feedback about an event</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("I need a product feedback survey")}>Product Feedback</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to gather feedback about a product</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="secondary" className="cursor-pointer" onClick={() => handleSendMessage("Help me create a team satisfaction survey")}>Team Satisfaction</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Create a poll to measure team satisfaction</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <Card className="w-full">
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                  <CardTitle className="flex items-center">
                    <CheckCircle2 className="h-5 w-5 mr-2 text-green-500" />
                    Poll Draft Ready
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={handleBackToChat} className="w-full sm:w-auto">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Chat
                  </Button>
                </div>
                <CardDescription>
                  Review your poll details below. You can go back to make changes or create the poll now.
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold break-words">{pollSuggestion?.title}</h3>
                    <p className="text-muted-foreground mt-1 break-words">{pollSuggestion?.description}</p>
                  </div>

                  <div>
                    <h3 className="text-md font-semibold mb-2">Questions ({pollSuggestion?.questions.length})</h3>
                    <div className="space-y-4">
                      {pollSuggestion?.questions.map((question, index) => (
                        <div key={index} className="border rounded-md p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="font-medium break-words">{question.text}</div>
                              <div className="flex gap-2 my-2">
                                <Badge variant="outline">{question.type}</Badge>
                                {question.required && <Badge>Required</Badge>}
                              </div>
                            </div>
                          </div>

                          {question.options && question.options.length > 0 && (
                            <div className="mt-2">
                              <div className="text-sm text-muted-foreground mb-1">Options:</div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                {question.options.map((option, i) => (
                                  <div key={i} className="text-sm border rounded px-2 py-1 overflow-hidden text-ellipsis whitespace-nowrap" title={option}>
                                    {option.length > 50 ? option.substring(0, 47) + '...' : option}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex flex-col gap-4">
                {showRetryPrompt && (
                  <div className="w-full bg-amber-50 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 p-3 rounded-md">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-5 w-5" />
                      <p className="font-medium">This is taking longer than expected</p>
                    </div>
                    <p className="mt-1 text-sm">The server might be experiencing delays. You can try one of these options:</p>
                    <div className="flex flex-col sm:flex-row gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2 border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-900/50 w-full sm:w-auto"
                        onClick={() => {
                          // Try to manually refresh auth and retry without page reload
                          toast.loading("Refreshing your session...");
                          ensureValidSession({ forceRefresh: true }).then(valid => {
                            if (valid) {
                              toast.success("Session refreshed successfully");
                              // Reset the creating state and retry
                              setIsCreatingPoll(false);
                              setTimeout(() => handleCreatePoll(), 500);
                            } else {
                              toast.error("Couldn't refresh your session. Try reloading the page.");
                            }
                          });
                        }}
                      >
                        <RefreshCw className="h-3 w-3" />
                        Retry without Reloading
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2 border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-900/50 w-full sm:w-auto"
                        onClick={handleRefreshAndRetry}
                      >
                        <RefreshCw className="h-3 w-3" />
                        Refresh Page & Retry
                      </Button>
                    </div>
                    <p className="text-xs mt-2 text-amber-600 dark:text-amber-400">
                      If the issue persists, you may be experiencing an authentication timeout.
                    </p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row sm:justify-between gap-3 w-full">
                  <Button variant="ghost" onClick={handleDiscardDraft} className="text-destructive hover:text-destructive hover:bg-destructive/10 w-full sm:w-auto">
                    Discard Draft
                  </Button>
                  <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <Button variant="outline" onClick={handleBackToChat} className="w-full sm:w-auto">
                      Make Changes
                    </Button>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="w-full sm:w-auto">
                            <Button
                              onClick={handleCreatePoll}
                              disabled={isCreatingPoll || pollCreationCompleted}
                              className="gap-2 w-full sm:w-auto"
                            >
                              {isCreatingPoll ? (
                                <>
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  Creating Poll...
                                </>
                              ) : pollCreationCompleted ? (
                                <>
                                  <CheckCircle2 className="h-4 w-4" />
                                  Poll Created!
                                </>
                              ) : (
                                <>
                                  <CheckCircle2 className="h-4 w-4" />
                                  Create Poll
                                </>
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{isCreatingPoll ? "Creating your poll..." : "Create your poll and save it to your dashboard"}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        )}
      </motion.div>
      </div>
    </motion.div>
  );
}
