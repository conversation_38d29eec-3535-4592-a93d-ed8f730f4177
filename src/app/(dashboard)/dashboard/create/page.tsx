'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Sparkles } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  extractContentFromUrl,
  extractContentFromWebsite,
  extractContentFromFile
} from '@/lib/services/client-extraction';

// Resource types
type ResourceType = 'url' | 'file' | 'website' | null;

// Helper function to extract content based on resource type
const extractContent = async (type: 'url' | 'website' | 'file', resource: string | File): Promise<string> => {
  if (type === 'url' && typeof resource === 'string') {
    return extractContentFromUrl(resource);
  } else if (type === 'website' && typeof resource === 'string') {
    return extractContentFromWebsite(resource);
  } else if (type === 'file' && resource instanceof File) {
    return extractContentFromFile(resource);
  }
  throw new Error('Invalid resource type or value');
};

export default function CreatePage() {
  const router = useRouter();
  const [resourceType, setResourceType] = useState<ResourceType>(null);
  const [resourceValue, setResourceValue] = useState<string>('');
  const [file, setFile] = useState<File | null>(null);
  const [extractedContent, setExtractedContent] = useState<string>('');
  const [isExtracting, setIsExtracting] = useState<boolean>(false);
  const [pollTitle, setPollTitle] = useState<string>('');
  const [pollDescription, setPollDescription] = useState<string>('');

  // Handle resource extraction
  const handleExtract = async () => {
    if (!resourceType) return;

    setIsExtracting(true);
    try {
      let content = '';

      if (resourceType === 'file' && file) {
        content = await extractContent('file', file);
      } else if ((resourceType === 'url' || resourceType === 'website') && resourceValue) {
        content = await extractContent(resourceType, resourceValue);
      }

      setExtractedContent(content);
    } catch (error) {
      console.error('Extraction error:', error);
      // Toast error message could be shown here
    } finally {
      setIsExtracting(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  // Handle navigation to create questions
  const handleCreateQuestions = () => {
    // Store poll data in local storage or state management
    const pollData = {
      title: pollTitle,
      description: pollDescription,
      extractedContent
    };

    // We can use localStorage to temporarily store the data between pages
    localStorage.setItem('currentPollData', JSON.stringify(pollData));

    // Navigate to the questions creation page
    router.push('/dashboard/create/questions');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <div className="h-8 w-1 bg-primary rounded-full"></div>
          <h1 className="text-3xl font-bold">Create a New Poll (Legacy)</h1>
        </div>
        <Button asChild variant="default" size="sm" className="gap-1">
          <Link href="/dashboard/create/conversational">
            <Sparkles className="h-4 w-4" />
            Try Conversational Creation (Recommended)
          </Link>
        </Button>
      </div>

      <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-blue-800">
          <strong>Note:</strong> The conversational poll creation experience is now the default recommended option.
          This page is maintained for legacy purposes but we encourage using the conversational interface for a better experience.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card className="overflow-hidden border-l-4 border-l-primary shadow-sm transition-all hover:shadow-md">
            <CardHeader className="bg-muted/30">
              <CardTitle className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M11 4h10v12h-10z"></path>
                  <path d="m3 7 5 3-5 3Z"></path>
                  <path d="M3 17h8"></path>
                </svg>
                Poll Details
              </CardTitle>
              <CardDescription>
                Provide basic information about your poll
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="title" className="block text-sm font-medium">
                    Poll Title <span className="text-destructive">*</span>
                  </label>
                  <Input
                    id="title"
                    placeholder="Enter a title for your poll"
                    value={pollTitle}
                    onChange={(e) => setPollTitle(e.target.value)}
                    className="transition-all focus:ring-2 focus:ring-primary/20"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="block text-sm font-medium">
                    Description <span className="text-muted-foreground text-xs">(optional)</span>
                  </label>
                  <Textarea
                    id="description"
                    placeholder="Describe what your poll is about"
                    value={pollDescription}
                    onChange={(e) => setPollDescription(e.target.value)}
                    className="min-h-[120px] transition-all focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="overflow-hidden border-l-4 border-l-primary shadow-sm transition-all hover:shadow-md extraction-section">
            <CardHeader className="bg-muted/30">
              <CardTitle className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="m12 15 2 2 4-4"></path>
                  <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
                  <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
                </svg>
                Resource Extraction
                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full font-medium">Optional</span>
              </CardTitle>
              <CardDescription>
                Import content from external resources to help create your poll (optional)
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <Tabs defaultValue="url" onValueChange={(value) => setResourceType(value as ResourceType)} className="w-full">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="url" className="flex items-center gap-2 data-[state=active]:bg-primary/10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                      <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                    </svg>
                    URL
                  </TabsTrigger>
                  <TabsTrigger value="file" className="flex items-center gap-2 data-[state=active]:bg-primary/10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                    </svg>
                    File
                  </TabsTrigger>
                  <TabsTrigger value="website" className="flex items-center gap-2 data-[state=active]:bg-primary/10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="2" x2="22" y1="12" y2="12"></line>
                      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                    </svg>
                    Website
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="url" className="focus-visible:outline-none focus-visible:ring-0">
                  <div className="space-y-4 p-4 bg-muted/20 rounded-md">
                    <div className="space-y-2">
                      <label htmlFor="url" className="block text-sm font-medium">
                        Enter URL
                      </label>
                      <Input
                        id="url"
                        placeholder="https://example.com/article"
                        value={resourceValue}
                        onChange={(e) => setResourceValue(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-primary/20"
                      />
                      <p className="text-xs text-muted-foreground">
                        We&apos;ll extract content from this specific URL
                      </p>
                    </div>

                    <Button
                      onClick={handleExtract}
                      disabled={!resourceValue || isExtracting}
                      className="w-full sm:w-auto"
                    >
                      {isExtracting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Extracting...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="m15 15-6-6"></path>
                            <path d="m9 15 6-6"></path>
                          </svg>
                          Extract Content
                        </>
                      )}
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="file" className="focus-visible:outline-none focus-visible:ring-0">
                  <div className="space-y-4 p-4 bg-muted/20 rounded-md">
                    <div className="space-y-2">
                      <label htmlFor="file" className="block text-sm font-medium">
                        Upload File
                      </label>
                      <div className="border-2 border-dashed border-muted-foreground/20 rounded-md p-4 text-center">
                        <Input
                          id="file"
                          type="file"
                          onChange={handleFileChange}
                          accept=".pdf,.docx,.txt"
                          className="hidden"
                        />
                        <label htmlFor="file" className="cursor-pointer flex flex-col items-center justify-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground">
                            <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
                            <path d="M12 12v9"></path>
                            <path d="m16 16-4-4-4 4"></path>
                          </svg>
                          <span className="text-sm font-medium">
                            {file ? file.name : "Click to browse or drag and drop"}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            Supports PDF, DOCX, and TXT files (max 10MB)
                          </span>
                        </label>
                      </div>
                    </div>

                    <Button
                      onClick={handleExtract}
                      disabled={!file || isExtracting}
                      className="w-full sm:w-auto"
                    >
                      {isExtracting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Extracting...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="m15 15-6-6"></path>
                            <path d="m9 15 6-6"></path>
                          </svg>
                          Extract Content
                        </>
                      )}
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="website" className="focus-visible:outline-none focus-visible:ring-0">
                  <div className="space-y-4 p-4 bg-muted/20 rounded-md">
                    <div className="space-y-2">
                      <label htmlFor="website" className="block text-sm font-medium">
                        Enter Website URL
                      </label>
                      <Input
                        id="website"
                        placeholder="https://example.com"
                        value={resourceValue}
                        onChange={(e) => setResourceValue(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-primary/20"
                      />
                      <p className="text-xs text-muted-foreground">
                        We&apos;ll crawl multiple pages within this website (max 5 pages)
                      </p>
                    </div>

                    <Button
                      onClick={handleExtract}
                      disabled={!resourceValue || isExtracting}
                      className="w-full sm:w-auto"
                    >
                      {isExtracting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Extracting...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="m15 15-6-6"></path>
                            <path d="m9 15 6-6"></path>
                          </svg>
                          Extract Content
                        </>
                      )}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="h-full shadow-sm transition-all hover:shadow-md border-t-4 border-t-primary overflow-hidden">
            <CardHeader className="bg-muted/30">
              <CardTitle className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                Extracted Content
              </CardTitle>
              <CardDescription>
                Content extracted from your resource will appear here
              </CardDescription>
            </CardHeader>
            <CardContent className="py-6">
              {isExtracting ? (
                <div className="flex flex-col justify-center items-center h-64">
                  <svg className="animate-spin h-8 w-8 mb-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className="text-muted-foreground">Extracting content...</p>
                  <p className="text-xs text-muted-foreground mt-2">This may take a few moments</p>
                </div>
              ) : extractedContent ? (
                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap bg-muted p-4 rounded-md overflow-auto max-h-[500px] text-sm border border-muted-foreground/20">
                    {extractedContent}
                  </pre>
                </div>
              ) : (
                <div className="flex flex-col justify-center items-center h-64 text-muted-foreground">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground/50 mb-4">
                    <rect width="8" height="8" x="8" y="8" rx="1"></rect>
                    <path d="M10.5 8V6.5a2.5 2.5 0 0 1 5 0V8"></path>
                    <path d="M8 9v12a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V9"></path>
                    <path d="M7 10h14"></path>
                    <path d="M7 13h14"></path>
                    <path d="M7 16h14"></path>
                  </svg>
                  <p className="text-center mb-2 font-medium">No content extracted yet</p>
                  <p className="text-sm text-center">You can proceed without extracting content</p>
                  <p className="text-xs text-center mt-2">Select a resource type and click &quot;Extract Content&quot; if needed</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <Button variant="outline" className="mr-4 gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M5 5h6a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3z"></path>
            <path d="M15 5h4a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3h-4"></path>
          </svg>
          Save as Draft
        </Button>
        <Button
          onClick={handleCreateQuestions}
          className="gap-2 transition-all bg-primary hover:bg-primary/90 group relative"
          disabled={!pollTitle.trim()} // Only disabled if there's no title
        >
          {extractedContent && (
            <span className="absolute -top-10 whitespace-nowrap bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">
              Content extracted ✓
            </span>
          )}
          Next: Create Questions
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-transform group-hover:translate-x-1">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </Button>
      </div>
    </div>
  );
}
