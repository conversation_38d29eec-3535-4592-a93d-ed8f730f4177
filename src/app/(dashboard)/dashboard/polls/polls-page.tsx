// polls-page.tsx
// Fixed version of the polls page component with improved error handling and session refresh

'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { PaginatedPolls } from '@/lib/services/polls';
import { createLoadPollsFunction } from './loadPolls';
import { useSessionRefresh } from '@/lib/hooks/useSessionRefresh';

const PAGE_SIZE = 10;

export default function PollsPage() {
  const [paginatedPolls, setPaginatedPolls] = useState<PaginatedPolls>({
    polls: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 0,
    pageSize: PAGE_SIZE
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingTime, setLoadingTime] = useState(0);
  const [loadingStartTime, setLoadingStartTime] = useState(0);

  // Use our new session refresh hook
  const {
    refreshSession,
    refreshing,
    maxAttemptsReached,
    resetRefreshAttempts
  } = useSessionRefresh();

  // Create the loadPolls function
  const loadPolls = createLoadPollsFunction({
    setPaginatedPolls,
    setIsLoading,
    setError
  });

  // Function to handle refresh button click
  const handleRefresh = () => {
    // Reset any refresh attempt tracking
    resetRefreshAttempts();
    loadPolls(true, currentPage, PAGE_SIZE);
  };

  // Function to handle session refresh
  const handleSessionRefresh = async () => {
    const success = await refreshSession();
    if (success) {
      // Wait a moment for the refresh to propagate
      setTimeout(() => {
        handleRefresh();
      }, 500);
    } else if (maxAttemptsReached) {
      setError("Unable to refresh your session after multiple attempts. Please try logging out and back in.");
    } else {
      setError("Failed to refresh your session. Please try logging out and back in.");
    }
  };

  // Set up a timer to track loading time
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (isLoading) {
      setLoadingStartTime(Date.now());
      timer = setInterval(() => {
        setLoadingTime(Math.floor((Date.now() - loadingStartTime) / 1000));
      }, 1000);
    } else {
      setLoadingTime(0);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLoading, loadingStartTime]);

  // Load polls on initial render and when currentPage changes
  useEffect(() => {
    loadPolls(false, currentPage, PAGE_SIZE);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage]);

  // Generate page navigation controls
  const renderPagination = () => {
    if (!paginatedPolls.polls.length || paginatedPolls.totalPages <= 1) return null;

    return (
      <div className="flex justify-center items-center space-x-2 mt-6">
        <Button
          variant="outline"
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1 || isLoading}
        >
          Previous
        </Button>
        <span>
          Page {currentPage} of {paginatedPolls.totalPages}
        </span>
        <Button
          variant="outline"
          onClick={() => setCurrentPage(prev => Math.min(prev + 1, paginatedPolls.totalPages))}
          disabled={currentPage === paginatedPolls.totalPages || isLoading}
        >
          Next
        </Button>
      </div>
    );
  };

  // Generate loading status message
  const getLoadingMessage = () => {
    if (loadingTime < 5) {
      return "Loading your polls...";
    } else if (loadingTime < 10) {
      return `Loading your polls... (${loadingTime}s)`;
    } else {
      return `Loading your polls... (${loadingTime}s) This is taking longer than usual.`;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Your Polls</h1>
        <div className="flex space-x-2">
          <Button onClick={handleRefresh} disabled={isLoading}>
            Refresh
          </Button>
          {error?.includes('session') && (
            <Button onClick={handleSessionRefresh} disabled={isLoading || refreshing}>
              Refresh Session
            </Button>
          )}
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 rounded-md flex justify-between items-center">
          <p>{error}</p>
          <Button variant="outline" size="sm" onClick={() => setError(null)}>Dismiss</Button>
        </div>
      )}

      {/* Long loading time warning */}
      {isLoading && loadingTime >= 10 && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md">
          <p>Loading is taking longer than expected. This could be due to:</p>
          <ul className="list-disc pl-5 mt-2">
            <li>Slow internet connection</li>
            <li>High server load</li>
            <li>Session timeout requiring refresh</li>
          </ul>
          <div className="mt-3 flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleSessionRefresh}>
              Refresh Session
            </Button>
            <Button variant="outline" size="sm" onClick={() => setError("Loading cancelled by user")}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-800"></div>
          <p className="mt-4 text-gray-500">{getLoadingMessage()}</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && paginatedPolls.polls.length === 0 && !error && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">You haven&apos;t created any polls yet.</p>
          <Button className="mt-4">Create Your First Poll</Button>
        </div>
      )}

      {/* Polls list */}
      {!isLoading && paginatedPolls.polls.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {paginatedPolls.polls.map(poll => (
            <div key={poll.id} className="border rounded-lg p-5 hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-lg mb-1">{poll.title}</h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{poll.description}</p>
              <div className="flex justify-between text-sm text-gray-500">
                <span>{poll.questions_count} questions</span>
                <span>{poll.responses_count} responses</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {renderPagination()}
    </div>
  );
}
