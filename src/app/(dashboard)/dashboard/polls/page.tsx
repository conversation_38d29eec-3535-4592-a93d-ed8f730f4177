"use client";

import { useEffect, useCallback, useMemo } from "react";
import { useRouter } from 'next/navigation'; // Import useRouter
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { motion } from "framer-motion";

// Import modularized parts
import usePollsPageState from "./state"; // Adjusted import path
import { createLoadPollsFunction } from "./loadPolls"; // Adjusted import path
import { PollCard, SearchAndFilter, LoadingAndError, EmptyState } from "./ui-components"; // Adjusted import path

// Import service functions and types (ensure these are correctly exported from polls.fixed.ts)
import {
  Poll,
  // PaginatedPolls, // This type is implicitly handled by usePollsPageState
  // getPolls, // This is used internally by createLoadPollsFunction
  deletePoll as deletePollService,
  duplicatePoll as duplicatePollService,
  closePoll as closePollService
} from "@/lib/services/polls";

import { handleSessionTimeout } from "@/lib/utils/auth-refresh";
import { handleError } from "@/lib/utils/error-handler";

export default function PollsPage() {
  const router = useRouter(); // Initialize useRouter
  const {
    searchQuery, debouncedSetSearchQuery,
    statusFilter, setStatusFilter,
    paginatedPolls, setPaginatedPolls, // Direct access to setPaginatedPolls
    polls,
    isLoading, setIsLoading,
    error, setError,
    loadingTime, setLoadingTime,
    isOnline, setIsOnline,
    currentPage, setCurrentPage,
    pageSize, // pageSize is managed by state but not directly set by UI in this version
    totalPages,
    // totalCount // Not directly used in UI rendering logic here, but available
  } = usePollsPageState();

  // Memoize the loadPolls function to prevent re-creation on every render
  const loadPolls = useMemo(() =>
    createLoadPollsFunction({ setPaginatedPolls, setIsLoading, setError }),
    [setPaginatedPolls, setIsLoading, setError] // Dependencies for createLoadPollsFunction
  );

  // Effect for initial load and when currentPage or pageSize changes
  useEffect(() => {
    // Ensure navigator is defined (client-side only)
    if (typeof navigator !== 'undefined') {
        setIsOnline(navigator.onLine);
        loadPolls(false, currentPage, pageSize);
    }
  }, [currentPage, pageSize, loadPolls, setIsOnline]);

  // Effect for loading timer
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isLoading) {
      const startTime = Date.now();
      setLoadingTime(0); // Reset loading time at the start of loading
      timer = setInterval(() => {
        setLoadingTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLoading, setLoadingTime]);

  // Effect for online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    if (typeof window !== 'undefined') {
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        // Set initial state
        setIsOnline(navigator.onLine);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, [setIsOnline]);

  const refreshAuthAndRetry = useCallback(async () => {
    toast.loading("Refreshing session...");
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('pollgpt_polls_cache');
      }
      const refreshed = await handleSessionTimeout(true);
      if (refreshed) {
        toast.success("Session refreshed.");
        setCurrentPage(1); // Reset to page 1 on refresh
        loadPolls(true, 1, pageSize);
      } else {
        toast.error("Failed to refresh session.");
        setError("Failed to refresh session. Please try logging in again.");
      }
    } catch (e) {
      handleError(e, { showToast: true });
      setError((e as Error).message || "An error occurred while refreshing.");
    }
  }, [loadPolls, pageSize, setCurrentPage, setError]);

  const handleCreateNewPoll = () => {
    router.push('/dashboard/create/conversational');
  };

  const handleDeletePoll = useCallback(async (id: string) => {
    if (!confirm("Are you sure you want to delete this poll?")) return;
    toast.loading("Deleting poll...");
    try {
      await deletePollService(id);
      toast.success("Poll deleted successfully.");
      // Refresh current page, handle if it becomes empty
      if (polls.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      } else {
        loadPolls(true, currentPage, pageSize);
      }
    } catch (e) {
      handleError(e, { showToast: true });
    }
  }, [polls, currentPage, pageSize, loadPolls, setCurrentPage]);

  const handleDuplicatePoll = useCallback(async (id: string) => {
    toast.loading("Duplicating poll...");
    try {
      await duplicatePollService(id);
      toast.success("Poll duplicated successfully.");
      setCurrentPage(1); // Go to first page to see new poll
      loadPolls(true, 1, pageSize);
    } catch (e) {
      handleError(e, { showToast: true });
    }
  }, [pageSize, loadPolls, setCurrentPage]);

  const handleClosePoll = useCallback(async (id: string) => {
    toast.loading("Closing poll...");
    try {
      await closePollService(id);
      toast.success("Poll closed successfully.");
      loadPolls(true, currentPage, pageSize);
    } catch (e) {
      handleError(e, { showToast: true });
    }
  }, [currentPage, pageSize, loadPolls]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Memoize filtered polls to avoid re-filtering on every render unless dependencies change
  const filteredPolls = useMemo(() => {
    return polls.filter(poll => {
      const matchesSearch = poll.title.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter ? poll.status === statusFilter : true;
      return matchesSearch && matchesStatus;
    });
  }, [polls, searchQuery, statusFilter]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } }
  };

  if (!isOnline && !isLoading && polls.length === 0) {
    return (
        <div className="container mx-auto px-4 py-8 text-center">
            <h2 className="text-2xl font-semibold mb-4">You are offline</h2>
            <p className="mb-4">Polls cannot be loaded. Please check your internet connection.</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
    );
  }

  return (
    <motion.div
      className="space-y-8 pb-10"
      initial="hidden"
      animate="show"
      variants={containerVariants}
    >
      {/* Hero section with gradient background */}
      <motion.div
        className="relative flex flex-col gap-4 p-8 rounded-xl bg-gradient-to-r from-blue-500/5 via-blue-500/10 to-blue-500/5 border border-blue-500/10 shadow-sm overflow-hidden mb-8"
        variants={itemVariants}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/10 to-transparent blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-tr from-blue-500/10 to-transparent blur-2xl"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              My Polls
              <span className="text-blue-500 ml-1">📋</span>
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage and monitor all your polls in one place
            </p>
          </div>
          <Button
            onClick={handleCreateNewPoll}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-500/20 transition-all hover:shadow-xl hover:shadow-blue-500/30"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <line x1="12" y1="5" x2="12" y2="19" />
              <line x1="5" y1="12" x2="19" y2="12" />
            </svg>
            Create New Poll
          </Button>
        </div>
      </motion.div>

      <div className="container mx-auto px-4">
        <SearchAndFilter
          searchQuery={searchQuery}
          setSearchQuery={debouncedSetSearchQuery}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
        />

        <LoadingAndError
          isLoading={isLoading}
          error={error}
          loadingTime={loadingTime}
          refreshAuthAndRetry={refreshAuthAndRetry}
        />

        {!isLoading && !error && filteredPolls.length === 0 && paginatedPolls.totalCount > 0 && (
           <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No polls match your current search or filter criteria.</p>
           </div>
        )}

        {!isLoading && !error && paginatedPolls.totalCount === 0 && (
          // Show EmptyState only if there are truly no polls at all (after initial load)
          <EmptyState createNewPoll={handleCreateNewPoll} />
        )}

        {!isLoading && !error && filteredPolls.length > 0 && (
          <>
            <div className="space-y-4 md:space-y-6">
              {filteredPolls.map((poll: Poll) => (
                <PollCard
                  key={poll.id}
                  poll={poll}
                  onDelete={handleDeletePoll}
                  onDuplicate={handleDuplicatePoll}
                  onClose={handleClosePoll}
                />
              ))}
            </div>
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
}
