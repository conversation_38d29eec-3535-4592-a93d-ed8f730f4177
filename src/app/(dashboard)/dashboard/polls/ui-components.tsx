// UI components for the updated polls page
import { Poll } from "@/lib/services/polls";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { motion } from "framer-motion";

// Poll card component
export const PollCard = ({ poll, onDelete, onDuplicate, onClose }: {
  poll: Poll,
  onDelete: (id: string) => void,
  onDuplicate: (id: string) => void,
  onClose: (id: string) => void
}) => {
  const pollLink = typeof window !== 'undefined' ? `${window.location.origin}/poll/${poll.id}` : `/poll/${poll.id}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="w-full"
    >
      <Card className="overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/20 hover:border-l-primary/60 bg-gradient-to-r from-background to-muted/20 relative">
        <CardContent className="p-4 sm:p-6 md:p-7">
          {/* Enhanced three-dots menu positioned at the top right */}
          <div className="absolute top-4 right-4 sm:top-6 sm:right-6 z-10">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-full hover:bg-muted/60 transition-colors duration-200 opacity-70 hover:opacity-100">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[180px] sm:w-[190px] shadow-lg">
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(pollLink)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                  </svg>
                  <span className="text-sm">Copy Link</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDuplicate(poll.id)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                  <span className="text-sm">Duplicate</span>
                </DropdownMenuItem>
                {poll.status === "active" && (
                  <DropdownMenuItem onClick={() => onClose(poll.id)} className="flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                    <span className="text-sm">Close Poll</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => onDelete(poll.id)} className="text-red-600 focus:text-red-700 flex gap-2 sm:gap-3 cursor-pointer py-2 sm:py-2.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line>
                  </svg>
                  <span className="text-sm">Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Main card content with improved mobile spacing */}
          <div className="pr- sm:pr-"> {/* Adjusted padding-right to account for the three-dots button */}
            {/* Status and metadata section */}
            <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
              <div className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full shadow-sm ${
                poll.status === 'active' ? 'bg-green-500' :
                poll.status === 'completed' ? 'bg-gray-500' :
                'bg-yellow-500'
              }`}></div>
              <span className={`px-2 py-0.5 sm:px-3 sm:py-1 text-xs font-semibold rounded-full ${
                poll.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                poll.status === 'completed' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
              }`}>
                {poll.status ? poll.status.charAt(0).toUpperCase() + poll.status.slice(1) : 'Draft'}
              </span>
            </div>

            {/* Title section with enhanced mobile layout */}
            <div className="mb-3 sm:mb-4">
              <div className="w-full">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold leading-tight mb-2 sm:mb-3">
                  <Link href={`/dashboard/polls/${poll.id}`} className="hover:text-primary transition-colors duration-200 block">
                    {poll.title}
                  </Link>
                </h3>
                <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed">
                  {poll.description || "No description provided."}
                </p>
              </div>
            </div>

            {/* Enhanced mobile-responsive stats section - center aligned */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center gap-3 sm:gap-6 mb-4 sm:mb-6 py-2.5 sm:py-3 px-3 sm:px-4 bg-muted/30 rounded-lg">
              <div className="flex items-center justify-center sm:justify-start gap-2 text-sm">
                <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-violet-100 dark:bg-violet-900/30 text-violet-600 dark:text-violet-400 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                     <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                     <polyline points="14 2 14 8 20 8" />
                     <line x1="16" x2="8" y1="13" y2="13" />
                     <line x1="16" x2="8" y1="17" y2="17" />
                     <line x1="10" x2="8" y1="9" y2="9" />
                  </svg>
                </div>
                <div>
                  <span className="font-semibold text-foreground">{poll.questions?.length || poll.questions_count || 0}</span>
                  <span className="text-muted-foreground ml-1">questions</span>
                </div>
              </div>
              <div className="flex items-center justify-center sm:justify-start gap-3 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                <div className="flex items-center gap-1.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                  </svg>
                  <span className="font-medium">{poll.responses_count || 0}</span>
                  <span className="hidden sm:inline">responses</span>
                  <span className="sm:hidden">resp.</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  <span className="font-medium">{poll.views_count || 0}</span>
                  <span>views</span>
                </div>
              </div>
            </div>

            {/* Enhanced responsive button section - stacked on mobile, row on larger screens */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 hover:border-primary/50 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/polls/${poll.id}`} className="flex items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  <span>View</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-400 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/results/${poll.id}`} className="flex items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path>
                    <path d="M22 12A10 10 0 0 0 12 2v10z"></path>
                  </svg>
                  <span>Results</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="h-11 sm:h-12 text-sm font-semibold rounded-lg border-2 border-green-200 text-green-700 hover:bg-green-50 hover:border-green-400 hover:text-green-800 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/30 transition-all duration-200 flex-1 px-4">
                <Link href={`/dashboard/polls/${poll.id}/simulate`} className="flex items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                    <line x1="12" x2="12" y1="7" y2="13"/>
                    <line x1="15" x2="9" y1="10" y2="10"/>
                  </svg>
                  <span>Simulate</span>
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Search and filter component
export const SearchAndFilter = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter
}: {
  searchQuery: string,
  setSearchQuery: (query: string) => void,
  statusFilter: string | null,
  setStatusFilter: (status: string | null) => void
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 mb-6">
      <div className="flex-1">
        <Input
          placeholder="Search polls..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full"
        />
      </div>
      <div className="flex gap-2">
        <Button
          variant={statusFilter === null ? "default" : "outline"}
          onClick={() => setStatusFilter(null)}
          className="whitespace-nowrap"
        >
          All
        </Button>
        <Button
          variant={statusFilter === 'active' ? "default" : "outline"}
          onClick={() => setStatusFilter('active')}
          className="whitespace-nowrap"
        >
          Active
        </Button>
        <Button
          variant={statusFilter === 'draft' ? "default" : "outline"}
          onClick={() => setStatusFilter('draft')}
          className="whitespace-nowrap"
        >
          Draft
        </Button>
        <Button
          variant={statusFilter === 'completed' ? "default" : "outline"}
          onClick={() => setStatusFilter('completed')}
          className="whitespace-nowrap"
        >
          Completed
        </Button>
      </div>
    </div>
  );
};

// Loading and error states component
export const LoadingAndError = ({
  isLoading,
  error,
  loadingTime,
  refreshAuthAndRetry
}: {
  isLoading: boolean,
  error: string | null,
  loadingTime: number,
  refreshAuthAndRetry: () => void
}) => {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-center text-gray-500 dark:text-gray-400">
          Loading your polls{loadingTime > 3 ? ` (${loadingTime}s)` : '...'}
        </p>
        {loadingTime > 10 && (
          <p className="text-center text-gray-500 dark:text-gray-400 mt-2">
            This is taking longer than usual. Please wait...
          </p>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Error loading polls
            </h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <div className="-mx-2 -my-1.5 flex">
                <Button
                  onClick={refreshAuthAndRetry}
                  variant="outline"
                  className="rounded-md px-2 py-1.5 text-sm font-medium"
                >
                  Refresh Session & Retry
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Empty state component
export const EmptyState = ({ createNewPoll }: { createNewPoll: () => void }) => {
  return (
    <div className="text-center py-12">
      <svg
        className="mx-auto h-12 w-12 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
        />
      </svg>
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No polls</h3>
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Get started by creating a new poll.
      </p>
      <div className="mt-6">
        <Button onClick={createNewPoll}>
          <svg
            className="-ml-1 mr-2 h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
              clipRule="evenodd"
            />
          </svg>
          New Poll
        </Button>
      </div>
    </div>
  );
};
