// Load polls function for the updated polls page
import { PaginatedPolls, getPolls } from "@/lib/services/polls";
import { checkConnectivityIssue } from "@/lib/utils/network-status";
import { ensureValidSession, withAuthRetry } from "@/lib/utils/session-manager";
import { categorizeError, ErrorType, handleError } from "@/lib/utils/error-handler";

// Track last load time for dynamic timeout
let lastLoadTime = 0;

// Broadcast channel for coordinating with auth provider
let authChannel: BroadcastChannel | null = null;
if (typeof window !== 'undefined') {
  authChannel = new BroadcastChannel('supabase-auth');
}

// Function to wait for session validation completion with shorter timeout
const waitForSessionValidation = (timeoutMs: number = 2000): Promise<boolean> => {
  return new Promise((resolve) => {
    let resolved = false;

    const timeout = setTimeout(() => {
      if (!resolved) {
        resolved = true;
        console.log('[LoadPolls] Session validation timeout, proceeding anyway');
        resolve(true); // Proceed optimistically
      }
    }, timeoutMs);

    if (authChannel) {
      const handleMessage = (event: MessageEvent) => {
        if (event.data.type === 'session_validation_complete' && !resolved) {
          resolved = true;
          clearTimeout(timeout);
          authChannel?.removeEventListener('message', handleMessage);
          console.log('[LoadPolls] Session validation completed via broadcast');
          resolve(event.data.sessionValid !== false); // Only fail if explicitly false
        }
      };

      authChannel.addEventListener('message', handleMessage);
    } else {
      // No broadcast channel available, proceed immediately
      clearTimeout(timeout);
      resolve(true);
    }
  });
};

interface LoadPollsSetters {
  setPaginatedPolls: (data: PaginatedPolls) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  // setLoadingTime will be handled by the main page component's useEffect
}

export const createLoadPollsFunction = (setters: LoadPollsSetters) => {
  const { setPaginatedPolls, setIsLoading, setError } = setters;

  return async (forceRefresh = false, currentPage: number, pageSize: number) => {
    try {
      setIsLoading(true);
      // Ensure error is null at the start of a new loading attempt
      setError(null);

      // Enhanced connectivity checks
      try {
        // Double check browser's online status first (it's immediate)
        if (typeof window !== 'undefined' && !navigator.onLine) {
          console.log("Browser reports offline status");
          setError("Your device appears to be offline. Please check your internet connection and try again.");
          setIsLoading(false);
          return;
        }

        // Then do a more thorough connectivity check
        const connectivityIssue = await Promise.race([
          checkConnectivityIssue(),
          new Promise<string>((resolve) => setTimeout(() => resolve(""), 3000)) // Reduced connectivity check timeout to 3s
        ]);

        if (connectivityIssue) {
          console.log("Network connectivity issue:", connectivityIssue);
          setError(`${connectivityIssue} The polls couldn't be loaded due to connectivity issues.`);
          setIsLoading(false);
          return;
        }
      } catch (connectivityError) {
        console.warn("Error during connectivity check:", connectivityError);
        // Continue execution - connectivity check isn't critical, just informative
      }

      // Set up a more reasonable timeout for the entire operation
      // Increased timeout when session might need refresh or after tab visibility changes
      const isLikelySessionRefresh = Date.now() - lastLoadTime > 5 * 60 * 1000;
      const recentTabVisibilityChange = Date.now() - lastLoadTime < 30 * 1000; // Within 30 seconds

      // Use longer timeout if session refresh is likely or recent tab visibility change
      const globalTimeoutMs = (isLikelySessionRefresh || recentTabVisibilityChange) ? 25000 : 15000; // 25s for session refresh/tab changes, 15s otherwise

      const loadingTimeout = setTimeout(() => {
        console.log(`Loading polls operation timed out globally after ${globalTimeoutMs}ms`);
        setError("Loading timed out. Your connection might be slow or the server may be experiencing issues.");
        setIsLoading(false);
      }, globalTimeoutMs);

      lastLoadTime = Date.now();

      // Skip session validation entirely unless forced - just proceed optimistically
      if (forceRefresh) {
        console.log('[LoadPolls] Force refresh requested, validating session...');
        const sessionValidationOptions = {
          forceRefresh: true,
          showToasts: false,
          redirectOnFailure: false
        };
        const sessionValid = await ensureValidSession(sessionValidationOptions);

        if (!sessionValid) {
          console.warn("[LoadPolls] Forced session validation failed");
          clearTimeout(loadingTimeout);
          setError("Your session has expired. Please log in again.");
          setPaginatedPolls({
            polls: [],
            totalCount: 0,
            currentPage: 1,
            totalPages: 0,
            pageSize: 10
          });
          setIsLoading(false);
          return;
        }
      } else {
        console.log("[LoadPolls] Skipping session validation for speed, proceeding optimistically...");
      }

      // initializeWithSampleData was removed as it's not available in polls.fixed.ts

      try {
        // Use our withAuthRetry utility which will automatically handle auth issues
        // This replaces our manual retry logic and handles session refreshing if needed
        const paginatedPolls = await withAuthRetry(async () => {
          // Set a timeout for the individual getPolls call with dynamic timeout
          const individualTimeoutMs = (isLikelySessionRefresh || recentTabVisibilityChange) ? 20000 : 12000; // Longer timeout for session refresh/tab changes
          console.log(`Using ${individualTimeoutMs}ms timeout for getPolls call`);

          // Don't add another timeout here since getPolls already has its own timeout logic
          // Just call getPolls with the page parameters
          return await getPolls(currentPage, pageSize);
        }, 3); // Allow up to 3 retries for auth issues (increased from 2)

        // Clear the global timeout since we got a response
        clearTimeout(loadingTimeout);

        // Update state with fetched polls and clear any previous errors
        setPaginatedPolls(paginatedPolls);
        console.log(`Loaded ${paginatedPolls.polls.length} polls successfully (page ${paginatedPolls.currentPage}/${paginatedPolls.totalPages})`);
        setError(null); // Explicitly clear error on success

        // Cache successful results with a 30-minute expiration (increased from 10 minutes)
        try {
          localStorage.setItem('pollgpt_polls_cache', JSON.stringify({
            timestamp: Date.now(),
            paginatedPolls,
            expiresAt: Date.now() + 1800000 // 30 minutes
          }));
        } catch (cacheError) {
          console.warn('Failed to cache polls:', cacheError);
        }
      } catch (pollsError) {
        clearTimeout(loadingTimeout);

        // Use our centralized error handler for consistent messaging
        const errorType = categorizeError(pollsError);

        // Handle error differently based on type with more specific messaging
        switch(errorType) {
          case ErrorType.Timeout:
            console.error("Timeout details:", {
              error: pollsError,
              timestamp: Date.now(),
              lastLoadTime
            });
            setError("Loading polls timed out. This might be due to database performance issues or missing indexes. Please try again or contact support if the problem persists.");
            break;
          case ErrorType.Auth:
            console.error("Auth error details:", { error: pollsError });
            setError("Authentication error when loading polls. Your session may have expired. Please try refreshing your session.");
            break;
          case ErrorType.RateLimit:
            setError("Rate limit reached. Please wait a moment before trying again.");
            break;
          case ErrorType.Database:
            console.error("Database error details:", { error: pollsError });
            setError("Database error occurred. This could be related to RLS policies or missing database functions. Please try logging out and back in.");
            break;
          default:
            // Use the error handler to get a friendly message
            handleError(pollsError, { showToast: false, logToConsole: true });
            setError(`Failed to load polls: ${(pollsError as Error)?.message || 'Unknown error'}. Please try refreshing the page.`);
        }
      }

      setIsLoading(false);
    } catch (error) {
      const err = error as Error;
      console.error("Error loading polls:", err);

      // Try to load from cache if available
      try {
        const cachedData = localStorage.getItem('pollgpt_polls_cache');
        if (cachedData) {
          const parsed = JSON.parse(cachedData);
          const cacheAge = Date.now() - parsed.timestamp;
          const cacheExpired = parsed.expiresAt ? Date.now() > parsed.expiresAt : cacheAge > 3600000;

          // Use cache if it's not expired and contains valid data
          if (!cacheExpired && parsed.paginatedPolls && Array.isArray(parsed.paginatedPolls.polls)) {
            console.log(`Loading ${parsed.paginatedPolls.polls.length} polls from cache (${Math.round(cacheAge/60000)}min old)`);
            setPaginatedPolls(parsed.paginatedPolls);
            setError(`Using cached data. ${err.message || 'Connection error'}. Click 'Refresh' to try again.`);
            setIsLoading(false);
            return;
          } else {
            console.log("Cache expired or invalid, not using it");
          }
        }
      } catch (cacheErr) {
        console.warn("Error loading from cache:", cacheErr);
      }

      // Provide more specific error messages
      if (err.message?.includes("session") || err.message?.includes("auth")) {
        setError("Authentication issue detected. Please try refreshing your session.");
      } else if (err.message?.includes("timed out")) {
        setError("Loading timed out. The server might be taking longer than expected to respond.");
      } else if (err.message?.includes("permission denied") || err.message?.includes("RLS")) {
        setError("Permission denied. Please try refreshing your session.");
      } else {
        setError(`Failed to load polls: ${err.message || 'Unknown error'}`);
      }

      setPaginatedPolls({
        polls: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0,
        pageSize: 10
      });
      setIsLoading(false);
    }
  };
};
