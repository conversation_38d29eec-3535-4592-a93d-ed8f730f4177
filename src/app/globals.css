@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Logo visibility variables for theme handling without hydration issues */
  --display-light-logo: block;
  --display-dark-logo: none;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Warm Modern Color Scheme for PollGPT - 2025 Edition */
:root {
  /* Base */
  --radius: 0.75rem;

  /* Core colors - Light mode */
  --background: #FFFCF2; /* Cream/Off-white background */
  --foreground: #252422; /* Very dark gray text color */

  /* Card & UI elements */
  --card: #FFFFFF; /* Pure white */
  --card-foreground: #252422; /* Very dark gray */
  --popover: #FFFFFF;
  --popover-foreground: #252422;

  /* Primary - Vibrant orange */
  --primary: #EB5E28; /* Vibrant orange */
  --primary-foreground: #FFFFFF;

  /* Secondary - Light beige */
  --secondary: #CCC5B9; /* Light beige/taupe */
  --secondary-foreground: #252422;

  /* Muted & accents */
  --muted: #F5F2EB; /* Lighter cream */
  --muted-foreground: #6C6861; /* Muted dark beige */
  --accent: #403D39; /* Dark gray accent */
  --accent-foreground: #FFFCF2; /* Cream/Off-white */

  /* Functional */
  --destructive: #E74C3C; /* Warning red */
  --border: #E6E2DA; /* Very light beige border */
  --input: #E6E2DA;
  --ring: #EB5E28; /* Vibrant orange */

  /* Chart colors for data visualization */
  --chart-1: #EB5E28; /* Primary orange */
  --chart-2: #D95525; /* Darker orange */
  --chart-3: #403D39; /* Dark gray */
  --chart-4: #596C56; /* Olive green complement */
  --chart-5: #7D8570; /* Sage green complement */

  /* Sidebar specific */
  --sidebar: #FFFCF2; /* Cream/Off-white */
  --sidebar-foreground: #252422; /* Very dark gray */
  --sidebar-primary: #EB5E28; /* Match primary */
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #403D39; /* Match accent */
  --sidebar-accent-foreground: #FFFCF2;
  --sidebar-border: #E6E2DA; /* Match border */
  --sidebar-ring: #EB5E28; /* Match ring */
}

.dark {
  /* Logo visibility variables for dark mode */
  --display-light-logo: none;
  --display-dark-logo: block;

  /* Core colors - Dark mode */
  --background: #252422; /* Very dark gray/almost black */
  --foreground: #FFFCF2; /* Cream/Off-white text */

  /* Card & UI elements */
  --card: #403D39; /* Dark gray */
  --card-foreground: #FFFCF2;
  --popover: #403D39;
  --popover-foreground: #FFFCF2;

  /* Primary - More vibrant in dark mode */
  --primary: #EB5E28; /* Vibrant orange */
  --primary-foreground: #FFFFFF;

  /* Secondary - Deeper in dark mode */
  --secondary: #56534E; /* Darker version of beige */
  --secondary-foreground: #FFFCF2;

  /* Muted & accents */
  --muted: #353330; /* Slightly lighter than background */
  --muted-foreground: #CCC5B9; /* Light beige */
  --accent: #CCC5B9; /* Light beige accent */
  --accent-foreground: #252422;

  /* Functional */
  --destructive: #E74C3C; /* Warning red */
  --border: #504C47; /* Dark beige border */
  --input: #504C47;
  --ring: #EB5E28; /* Vibrant orange */

  /* Chart colors - adjusted for dark mode */
  --chart-1: #EB5E28; /* Primary orange */
  --chart-2: #F17A50; /* Lighter orange */
  --chart-3: #CCC5B9; /* Light beige */
  --chart-4: #7D9D7F; /* Sage green */
  --chart-5: #A6BFA8; /* Light sage */

  /* Sidebar specific */
  --sidebar: #2B2A27; /* Slightly lighter than background */
  --sidebar-foreground: #FFFCF2;
  --sidebar-primary: #EB5E28; /* Match primary */
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #CCC5B9; /* Match accent */
  --sidebar-accent-foreground: #252422;
  --sidebar-border: #504C47; /* Match border */
  --sidebar-ring: #EB5E28; /* Match ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Dramatically enhanced dynamic background with more prominent animated elements */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 15% 50%,
        rgba(235, 94, 40, 0.25) 0%,
        transparent 35%),
      radial-gradient(circle at 85% 30%,
        rgba(89, 108, 86, 0.18) 0%,
        transparent 40%),
      radial-gradient(circle at 75% 85%,
        rgba(235, 94, 40, 0.22) 0%,
        transparent 45%),
      radial-gradient(circle at 25% 20%,
        rgba(204, 197, 185, 0.2) 0%,
        transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: subtleShift 15s ease-in-out infinite alternate;
  }

  .dark body::before {
    background:
      radial-gradient(circle at 15% 50%,
        rgba(235, 94, 40, 0.28) 0%,
        transparent 35%),
      radial-gradient(circle at 85% 30%,
        rgba(126, 157, 127, 0.2) 0%,
        transparent 40%),
      radial-gradient(circle at 75% 85%,
        rgba(235, 94, 40, 0.25) 0%,
        transparent 45%),
      radial-gradient(circle at 25% 20%,
        rgba(204, 197, 185, 0.15) 0%,
        transparent 50%);
    animation: subtleShift 15s ease-in-out infinite alternate;
  }

  /* More noticeable shifting animation for background gradients */
  @keyframes subtleShift {
    0% {
      background-position: 0% 0%;
    }
    100% {
      background-position: 5% 5%;
    }
  }

  /* Much more visible geometric pattern overlay */
  .geometric-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23eb5e28' fill-opacity='0.18'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.85;
    z-index: -1;
    pointer-events: none;
  }

  .dark .geometric-overlay {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23eb5e28' fill-opacity='0.22'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.9;
  }

  /* Much larger and more visible floating particles */
  .floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
  }

  .particle {
    position: absolute;
    display: block;
    border-radius: 50%;
    background-color: rgba(235, 94, 40, 0.35);
    animation: float 18s infinite ease-in-out;
    filter: blur(12px);
    box-shadow: 0 0 40px 20px rgba(235, 94, 40, 0.15);
  }

  .dark .particle {
    background-color: rgba(235, 94, 40, 0.4);
    filter: blur(15px);
    box-shadow: 0 0 50px 25px rgba(235, 94, 40, 0.2);
  }

  .particle:nth-child(1) {
    width: 300px;
    height: 300px;
    left: 5%;
    top: 15%;
    opacity: 0.3;
    animation-delay: 0s;
  }

  .particle:nth-child(2) {
    width: 350px;
    height: 350px;
    left: 75%;
    top: 65%;
    opacity: 0.25;
    animation-delay: -5s;
    background-color: rgba(204, 197, 185, 0.35);
  }

  .particle:nth-child(3) {
    width: 250px;
    height: 250px;
    left: 35%;
    top: 80%;
    opacity: 0.3;
    animation-delay: -10s;
  }

  .particle:nth-child(4) {
    width: 320px;
    height: 320px;
    left: 70%;
    top: 20%;
    opacity: 0.25;
    animation-delay: -15s;
    background-color: rgba(89, 108, 86, 0.3);
  }

  /* Add a fifth larger particle for even more visibility */
  .particle:nth-child(5) {
    width: 380px;
    height: 380px;
    left: 40%;
    top: 40%;
    opacity: 0.2;
    animation-delay: -8s;
    background-color: rgba(235, 94, 40, 0.25);
    animation-duration: 25s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0) translateX(0) rotate(0);
    }
    33% {
      transform: translateY(-30px) translateX(15px) rotate(8deg);
    }
    66% {
      transform: translateY(15px) translateX(-15px) rotate(-8deg);
    }
  }

  /* CSS Animations to replace framer-motion */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes shine {
    from {
      background-position: -200% 0;
    }
    to {
      background-position: 200% 0;
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-fadeInDelayed {
    opacity: 0;
    animation: fadeIn 0.5s ease-out 0.2s forwards;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  .animate-fadeInUpDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.1s forwards;
  }

  .animate-fadeInUpMoreDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.2s forwards;
  }

  .animate-fadeInUpMostDelayed {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 0.3s forwards;
  }

  .animate-pulse-slow {
    animation: pulse 8s ease-in-out infinite;
  }

  .animate-shine {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    background-size: 200% 100%;
    animation: shine 3s infinite;
  }

  /* Enhance cards with subtle effects */
  .card {
    position: relative;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right,
      #EB5E28,
      #F17A50);
    opacity: 0;
    transition: opacity 0.25s ease;
  }

  .card:hover::after {
    opacity: 1;
  }

  .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .dark .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Enhance buttons with subtle gradient */
  .btn-primary,
  .btn-action,
  button[data-variant="default"],
  .btn-default {
    background-image: linear-gradient(to right,
      #EB5E28,
      #F17A50
    );
    transition: all 0.2s ease;
  }

  /* Add smooth transitions for theme changes */
  html.disable-transitions * {
    transition-duration: 0ms !important;
  }

  html, body {
    transition: background-color 0.3s ease;
  }

  * {
    transition: color 0.2s ease, border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  }

  /* SEO helper classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
}
