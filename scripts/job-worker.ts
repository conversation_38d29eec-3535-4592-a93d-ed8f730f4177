#!/usr/bin/env node

/**
 * Background Job Worker Process
 *
 * Dedicated worker process for handling background jobs:
 * - Simulation processing
 * - Cache warming
 * - Data aggregation
 * - Performance monitoring
 * - Alert processing
 * - Cleanup tasks
 */

import { JobQueue, jobQueue, JobType, JobPriority } from '../src/lib/services/job-queue';
import { performanceMonitor, PerformanceMonitor } from '../src/lib/services/performance-monitor';
import { alertSystem, AlertSystem as ActualAlertSystemType } from '../src/lib/services/alert-system';

// Job options interface - used when adding jobs
interface JobOptions {
  priority?: JobPriority; // Now uses imported JobPriority
  maxAttempts?: number;
  scheduledFor?: Date;
  userId?: string;
  pollId?: string;
}

// Use JobOptions in addJob calls to ensure type safety

// Define a payload structure for the dynamic alert methods
interface AlertPayload {
  level: 'info' | 'warning' | 'error'; // Changed 'warn' to 'warning'
  message: string;
  title?: string; // Added optional title property
  details?: Record<string, unknown>; // Changed 'any' to 'unknown'
  timestamp: string;
  source: string;
  channels?: string[]; // Added optional channels property
  metadata?: Record<string, unknown>; // Added optional metadata property
}

// Interface to describe the expected shape of alertSystem for dynamic method checking.
// It includes known methods from AlertSystem (via ActualAlertSystemType)
// and optional methods (send, sendAlert, alert) we are probing for.
interface DynamicAlertSystem extends ActualAlertSystemType {
  send?: (payload: AlertPayload) => Promise<void> | void;
  sendAlert?: (payload: AlertPayload) => Promise<void> | void;
  alert?: (payload: AlertPayload) => Promise<void> | void;
}

/**
 * Job Worker Manager
 * Manages background job processing with monitoring and health checks
 */
class JobWorkerManager {
  private jobQueue: JobQueue | null = null;
  private performanceMonitor: PerformanceMonitor | null = null;
  private isRunning = false;
  private workerStats = {
    startTime: null,
    processedJobs: 0,
    failedJobs: 0,
    lastJobTime: null,
    errors: [],
  };

  constructor() {
    this.workerStats.startTime = new Date();
  }

  async initialize() {
    if (this.isRunning) {
      console.log('Job worker already running');
      return;
    }

    console.log('🔧 Initializing Background Job Worker...');

    try {
      // Initialize performance monitoring
      console.log('📊 Starting Performance Monitor...');
      this.performanceMonitor = performanceMonitor;
      // PerformanceMonitor is initialized via getInstance(), no separate start() call needed.
      console.log('✅ Performance Monitor ready');

      // Initialize job queue
      console.log('📋 Starting Job Queue...');
      this.jobQueue = jobQueue;
      await this.jobQueue.startProcessing(); // Assuming startProcessing is the correct method
      console.log('✅ Job Queue ready');

      // Set up job event listeners
      this.setupJobEventListeners();

      // Register custom job handlers
      this.registerJobHandlers();

      // Set up health monitoring
      this.setupHealthMonitoring();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      this.isRunning = true;
      console.log('🎉 Background Job Worker initialized successfully!');

      // Start processing jobs
      await this.startProcessing();

    } catch (error) {
      console.error('❌ Failed to initialize Job Worker:', error);
      await this.sendAlert('worker_initialization_failed', {
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      process.exit(1);
    }
  }

  setupJobEventListeners() {
    console.log('🔗 Setting up job event listeners...');

    if (!this.jobQueue) return;
    // TODO: Re-implement event handling if JobQueue is updated to support it.
    // The current JobQueue class does not emit events like 'completed', 'failed', etc.
    // console.log('[JobWorkerManager] Event listeners are currently disabled.');

    // // Listen for job completion
    // this.jobQueue.on('completed', (job) => {
    //   console.log(`✅ Job ${job.id} completed successfully`);
    //   this.workerStats.processedJobs++;
    //   this.workerStats.lastJobTime = new Date();
    //   // Potentially trigger follow-up actions or notifications
    //   if (job.type === 'simulation_task' && job.result?.requiresNotification) {
    //     this.sendAlert('simulation_complete', { pollId: job.pollId, userId: job.userId });
    //   }
    // });

    // // Listen for job failure
    // this.jobQueue.on('failed', ({ job, error }) => {
    //   console.error(`❌ Job ${job.id} failed:`, error);
    //   this.workerStats.failedJobs++;
    //   this.workerStats.lastJobTime = new Date();
    //   this.workerStats.errors.push({ jobId: job.id, error: error.message, timestamp: new Date() });

    //   // Send alert for critical job failures
    //   if (job.priority === 'critical') {
    //     this.sendAlert('critical_job_failed', { jobId: job.id, error: error.message });
    //   }

    //   // Implement retry logic if needed, or mark as permanently failed
    //   if (job.attempts >= job.maxAttempts) {
    //     console.error(`Job ${job.id} reached max attempts and failed permanently.`);
    //     // Update job status in DB or notify admin
    //   } else {
    //     // Optionally, re-queue with delay (JobQueue might handle this internally)
    //     console.log(`Retrying job ${job.id}, attempt ${job.attempts + 1}`);
    //   }
    // });

    // // Listen for job progress (if supported by JobQueue)
    // this.jobQueue.on('progress', ({ job, progress }) => {
    //   console.log(`🔄 Job ${job.id} progress: ${progress}%`);
    //   // Update UI or monitoring dashboard
    // });

    // // Listen for queue events (e.g., queue empty, new job added)
    // this.jobQueue.on('queue_empty', () => {
    //   console.log('😴 Job queue is empty, worker is idle.');
    // });

    // this.jobQueue.on('new_job', (job) => {
    //   console.log(`📬 New job ${job.id} added to the queue.`);
    // });
  }

  registerJobHandlers() {
    console.log('🔧 Registering custom job handlers...');

    // Enhanced error handling wrapper
    const withErrorHandling = (handler) => async (job) => {
      const startTime = Date.now();
      try {
        const result = await handler(job);
        const duration = Date.now() - startTime;

        // Log performance
        if (duration > 5000) { // Log slow jobs
          console.warn(`⚠️ Slow job detected: ${job.type} took ${duration}ms`);

          await this.sendAlert('slow_job_detected', {
            jobId: job.id,
            type: job.type,
            duration,
            threshold: 5000,
          });
        }

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        // Enhanced error logging
        console.error(`❌ Job handler error: ${job.type}`, {
          jobId: job.id,
          error: error.message,
          stack: error.stack,
          duration,
          data: job.data,
        });

        throw error;
      }
    };

    // Register all job types with enhanced handlers
    // Align with JobType from job-queue.ts or ensure these are valid subtypes
    const jobTypes: JobType[] = [
      // These need to match the JobType definition in job-queue.ts
      // For example, 'simulation_processing' is not in the imported JobType.
      // Let's assume for now these are custom types handled by this worker
      // and we'll cast them as needed, or they need to be added to the main JobType.
      // For now, I will use the types that are actually present in the imported JobType.
      'simulation_task', // Changed from 'simulation_processing'
      'cache_warmup',    // This is present in job-queue.ts as 'cache_warmup'
      // 'data_aggregation', // Not in imported JobType
      'poll_analysis',   // This is present in job-queue.ts
      // 'alert_processing', // Not in imported JobType
      'data_cleanup',    // This is present in job-queue.ts
      // 'user_notification', // Not in imported JobType
      // 'background_sync', // Not in imported JobType
      'report_generation', // This is present
      'batch_email',       // This is present
      'poll_scraping',     // This is present
      'ai_processing'      // This is present
    ];

    jobTypes.forEach(type => {
      // Now that jobTypes array is of type JobType[], direct usage should be fine.
      this.jobQueue.registerHandler(type, withErrorHandling(
        this.getJobHandler(type).bind(this)
      ));
    });

    console.log(`✅ Registered ${jobTypes.length} job handlers`);
  }

  getJobHandler(type: JobType) {
    const handlers = {
      simulation_processing: this.handleSimulationProcessing,
      cache_warmup: this.handleCacheWarmup,
      data_aggregation: this.handleDataAggregation,
      performance_analysis: this.handlePerformanceAnalysis,
      alert_processing: this.handleAlertProcessing,
      cleanup_task: this.handleCleanupTask,
      user_notification: this.handleUserNotification,
      background_sync: this.handleBackgroundSync,
    };

    return handlers[type] || this.handleGenericJob;
  }

  // Job handler implementations
  async handleSimulationProcessing(job) {
    console.log(`🎯 Processing simulation: ${job.data.pollId}`);

    // Simulate processing time based on complexity
    const processingTime = Math.random() * 2000 + 500; // 500-2500ms
    await new Promise(resolve => setTimeout(resolve, processingTime));

    // Record simulation metrics
    await this.performanceMonitor.recordMetric('simulations.processed', 1);
    await this.performanceMonitor.recordMetric('simulations.duration', processingTime);

    return {
      pollId: job.data.pollId,
      processed: true,
      duration: processingTime,
      timestamp: new Date().toISOString(),
    };
  }

  async handleCacheWarmup(job) {
    console.log(`🔥 Cache warmup: ${job.data.strategy}`);

    // Simulate cache warming
    const keys = job.data.keys || [];
    const warmedKeys = [];

    for (const key of keys) {
      // Simulate warming each key
      await new Promise(resolve => setTimeout(resolve, 50));
      warmedKeys.push(key);
    }

    await this.performanceMonitor.recordMetric('cache.warmed_keys', warmedKeys.length);

    return {
      strategy: job.data.strategy,
      warmedKeys: warmedKeys.length,
      timestamp: new Date().toISOString(),
    };
  }

  async handleDataAggregation(job) {
    console.log(`📊 Data aggregation: ${job.data.type}`);

    // Simulate data aggregation processing
    const processingTime = Math.random() * 1000 + 200;
    await new Promise(resolve => setTimeout(resolve, processingTime));

    await this.performanceMonitor.recordMetric('data.aggregations', 1, {
      type: job.data.type,
    });

    return {
      type: job.data.type,
      processed: true,
      duration: processingTime,
    };
  }

  async handlePerformanceAnalysis(job) {
    console.log(`📈 Performance analysis: ${job.data.metric}`);

    // Get current performance metrics
    const metricsSummary = await this.performanceMonitor.getSystemStatus();

    // Analyze trends or anomalies
    const analysis = {
      metric: job.data.metric,
      status: 'healthy',
      recommendations: [],
      timestamp: new Date().toISOString(),
    };

    // Simple analysis logic
    const activeConnections = parseInt(metricsSummary.database?.connections || '0', 10);
    if (!isNaN(activeConnections) && activeConnections > 8) {
      analysis.status = 'warning';
      analysis.recommendations.push('High database connection usage detected');
    }

    return analysis;
  }

  async handleAlertProcessing(job) {
    console.log(`🚨 Processing alert: ${job.data.alertType}`);

    // Send alert through alert system
    await alertSystem.processMetric(job.data.alertType, 1, {
      level: job.data.level || 'info',
      title: job.data.title,
      message: job.data.message,
      channels: job.data.channels || ['log'],
      metadata: job.data.metadata || {}
    });

    return {
      alertType: job.data.alertType,
      sent: true,
      timestamp: new Date().toISOString(),
    };
  }

  async handleCleanupTask(job) {
    console.log(`🧹 Cleanup task: ${job.data.target}`);

    // Simulate cleanup operations
    const cleaned = Math.floor(Math.random() * 100) + 10;
    await new Promise(resolve => setTimeout(resolve, 300));

    await this.performanceMonitor.recordMetric('cleanup.items_cleaned', cleaned, {
      target: job.data.target,
    });

    return {
      target: job.data.target,
      itemsCleaned: cleaned,
      timestamp: new Date().toISOString(),
    };
  }

  async handleUserNotification(job) {
    console.log(`📬 User notification: ${job.data.userId}`);

    // Simulate notification sending
    await new Promise(resolve => setTimeout(resolve, 200));

    await this.performanceMonitor.recordMetric('notifications.sent', 1, {
      type: job.data.type,
    });

    return {
      userId: job.data.userId,
      type: job.data.type,
      sent: true,
      timestamp: new Date().toISOString(),
    };
  }

  async handleBackgroundSync(job) {
    console.log(`🔄 Background sync: ${job.data.source}`);

    // Simulate sync operation
    const syncTime = Math.random() * 800 + 200;
    await new Promise(resolve => setTimeout(resolve, syncTime));

    await this.performanceMonitor.recordMetric('sync.operations', 1, {
      source: job.data.source,
    });

    return {
      source: job.data.source,
      synced: true,
      duration: syncTime,
      timestamp: new Date().toISOString(),
    };
  }

  async handleGenericJob(job) {
    console.log(`⚙️ Generic job: ${job.type}`);

    // Basic processing
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      type: job.type,
      processed: true,
      timestamp: new Date().toISOString(),
    };
  }

  setupHealthMonitoring() {
    console.log('🏥 Setting up health monitoring...');

    // Health check interval
    setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Every 30 seconds

    // Stats reporting interval
    setInterval(async () => {
      await this.reportStats();
    }, 60000); // Every minute

    console.log('✅ Health monitoring configured');
  }

  async performHealthCheck() {
    try {
      const health = {
        status: 'healthy',
        uptime: Date.now() - this.workerStats.startTime.getTime(),
        processedJobs: this.workerStats.processedJobs,
        failedJobs: this.workerStats.failedJobs,
        successRate: this.workerStats.processedJobs > 0
          ? ((this.workerStats.processedJobs - this.workerStats.failedJobs) / this.workerStats.processedJobs * 100).toFixed(2)
          : 100,
        lastJobTime: this.workerStats.lastJobTime,
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString(),
      };

      // Check for warning conditions
      const successRateValue = typeof health.successRate === 'string' ? parseFloat(health.successRate) : health.successRate;
      if (successRateValue < 90) {
        health.status = 'warning';
        await this.sendAlert('low_success_rate', {
          successRate: health.successRate,
          threshold: 90,
        });
      }

      if (health.failedJobs > 10) {
        health.status = 'warning';
        await this.sendAlert('high_failure_count', {
          failedJobs: health.failedJobs,
          threshold: 10,
        });
      }

      // Record health metrics
      await this.performanceMonitor.recordMetric('worker.health', health.status === 'healthy' ? 1 : 0);
      await this.performanceMonitor.recordMetric('worker.processed_jobs', this.workerStats.processedJobs);
      await this.performanceMonitor.recordMetric('worker.failed_jobs', this.workerStats.failedJobs);

    } catch (error) {
      console.error('❌ Health check failed:', error);
    }
  }

  async reportStats() {
    const stats = {
      uptime: Math.floor((Date.now() - this.workerStats.startTime.getTime()) / 1000),
      processedJobs: this.workerStats.processedJobs,
      failedJobs: this.workerStats.failedJobs,
      recentErrors: this.workerStats.errors.slice(-5),
      memoryUsage: process.memoryUsage(),
    };

    console.log('📊 Worker Stats:', JSON.stringify(stats, null, 2));

    // Clean up old errors
    if (this.workerStats.errors.length > 100) {
      this.workerStats.errors = this.workerStats.errors.slice(-50);
    }
  }

  async startProcessing() {
    console.log('🚀 Starting job processing...');

    // Start multiple workers for concurrent processing
    const workerCount = parseInt(process.env.JOB_WORKER_COUNT) || 4;

    for (let i = 0; i < workerCount; i++) {
      // this.jobQueue.startProcessing(); // startProcessing is already called in initialize()
      // Use a more generic approach since startWorker might not be available
      try {
        // Try to start a worker if the method exists
        if (this.jobQueue) {
          // @ts-expect-error - startWorker might not be in the type definition but could exist at runtime
          if (typeof this.jobQueue.startWorker === 'function') {
            // @ts-expect-error - Using startWorker method that might not be in type definition
            this.jobQueue.startWorker(`worker-${i}`);
          } else {
            console.log(`Worker ${i} could not be started - startWorker method not available`);
          }
        }
      } catch (error) {
        console.error(`Failed to start worker ${i}:`, error);
      }
    }

    console.log(`✅ Started ${workerCount} job workers`);

    // Add some initial jobs for testing
    await this.addInitialJobs();
  }

  async addInitialJobs() {
    console.log('➕ Adding initial test jobs...');

    // Add cache warmup job
    await this.jobQueue.addJob('cache_warmup', {
      strategy: 'popular_polls',
      keys: ['poll:popular', 'api:responses', 'user:stats'],
    }, { priority: 'normal', maxAttempts: 2 } as JobOptions);

    // Add performance analysis job
    await this.jobQueue.addJob('poll_analysis', {
      metric: 'overall_health',
    }, { priority: 'high', maxAttempts: 1 } as JobOptions);

    // Add cleanup job
    await this.jobQueue.addJob('data_cleanup', {
      target: 'expired_cache_keys',
    }, { priority: 'low', maxAttempts: 1 } as JobOptions);

    console.log('✅ Initial jobs added');
  }

  async sendAlert(type: string, data: Record<string, unknown>) {
    try {
      // The alert system might have different methods in different environments
      // Try to use a method that exists
      if (!alertSystem) {
        console.error('Alert system not properly initialized');
        return;
      }
      
      // Cast alertSystem to our DynamicAlertSystem interface for type-safe access
      const typedAlertSystem = alertSystem as DynamicAlertSystem;
      // Dynamically find the alert method on the alertSystem
      // This handles cases where the method might be 'send', 'sendAlert', or 'alert'
      const alertMethod = typedAlertSystem.send || typedAlertSystem.sendAlert || typedAlertSystem.alert;

      
      if (typeof alertMethod !== 'function') {
        console.error('No valid alert method found on alert system');
        return;
      }
      
      await alertMethod({
        level: 'warning',
        title: `Job Worker Alert: ${type}`,
        message: `Job worker encountered: ${type}`,
        channels: ['log', 'webhook'],
        timestamp: new Date().toISOString(), // Added timestamp
        source: 'JobWorkerManager', // Added source
        metadata: {
          alertType: type,
          workerStats: this.workerStats,
          ...data,
        },
      });
    } catch (error) {
      console.error('❌ Failed to send alert:', error);
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`\n🛑 Received ${signal}. Shutting down job worker gracefully...`);

      try {
        this.isRunning = false;

        // Stop accepting new jobs
        if (this.jobQueue) {
          console.log('⏸️ Stopping job queue...');
          this.jobQueue.stopProcessing();
        }

        // Wait for active jobs to complete (with timeout)
        console.log('⏳ Waiting for active jobs to complete...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second grace period

        // Send final stats
        await this.reportStats();

        console.log('✅ Job worker stopped gracefully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
  }

  getStats() {
    return {
      ...this.workerStats,
      isRunning: this.isRunning,
      uptime: this.workerStats.startTime ? Date.now() - this.workerStats.startTime.getTime() : 0,
    };
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'start';

  const worker = new JobWorkerManager();

  switch (command) {
    case 'start':
      await worker.initialize();
      // Keep the process running
      console.log('🎯 Background job worker is running. Press Ctrl+C to stop.');
      break;

    case 'stats':
      await worker.initialize();
      const stats = worker.getStats();
      console.log('📊 Job Worker Stats:');
      console.log(JSON.stringify(stats, null, 2));
      process.exit(0);

    default:
      console.log(`
🔧 Background Job Worker

Usage: node scripts/job-worker.js [command]

Commands:
  start      Start the background job worker (default)
  stats      Show worker statistics

Examples:
  node scripts/job-worker.js start
  node scripts/job-worker.js stats
      `);
      process.exit(1);
  }
}

// Export worker for programmatic use
export { JobWorkerManager };

// Run CLI if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Job worker failed:', error);
    process.exit(1);
  });
}
