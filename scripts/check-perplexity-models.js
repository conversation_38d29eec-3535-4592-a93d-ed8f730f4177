// Test script to get available models from Perplexity API
import fetch from 'node-fetch';
import * as dotenv from 'dotenv';
dotenv.config();

async function testPerplexityModels() {
  try {
    // Attempt to get API key from various sources
    let apiKey = process.env.PERPLEXITY_API_KEY ||
                 process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;

    if (!apiKey) {
      console.error('❌ No Perplexity API key found in environment variables');
      return;
    }

    console.log('🔑 Using Perplexity API key (first 5 chars):', apiKey.substring(0, 5) + '...');

    // Try listing available models (if the API supports this endpoint)
    console.log('🔍 Attempting to list available models...');
    try {
      const modelsResponse = await fetch('https://api.perplexity.ai/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      console.log(`📊 Models endpoint status: ${modelsResponse.status} ${modelsResponse.statusText}`);

      if (modelsResponse.ok) {
        const modelsData = await modelsResponse.json();
        console.log('✅ Available models:', modelsData);
      } else {
        const errorText = await modelsResponse.text();
        console.error('❌ Error getting models:', errorText);
      }
    } catch (modelsError) {
      console.error('💥 Exception trying to get models:', modelsError.message);
    }

    // Fall back to a basic test with pplx-7b-online (a common Perplexity model name)
    console.log('\n🧪 Testing with pplx-7b-online model...');
    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'pplx-7b-online',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Hello, what are you?' }
        ],
        max_tokens: 100
      })
    });

    console.log(`📊 Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success! Response:', JSON.stringify(data).substring(0, 200) + '...');
    } else {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
    }
  } catch (error) {
    console.error('💥 Exception:', error.message);
  }
}

testPerplexityModels().catch(console.error);
