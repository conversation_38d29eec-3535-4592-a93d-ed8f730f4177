<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# what gemini models are available and what is the rate limit for them? please include the exact model name we need to use for the api

Based on the current Gemini API documentation, here are the available models and their rate limits:

## Available Gemini Models

### **Gemini 2.5 Models**

**Gemini 2.5 Pro**

- `gemini-2.5-pro-preview-05-06` - Latest preview version (Preview stage, released 2025-05-06)[^1]
- `gemini-2.5-pro-preview-03-25` - Points to the latest preview version (Preview stage, released 2025-03-25)[^1]

**Gemini 2.5 Flash**

- `gemini-2.5-flash-preview-04-17` - Preview version (Preview stage, released 2025-04-17)[^1]


### **Gemini 2.0 Models**

**Gemini 2.0 Flash**

- `gemini-2.0-flash-001` - Latest stable version (Stable, released 2025-02-05)[^1]
- `gemini-2.0-flash` - Auto-updated alias pointing to latest stable version (Stable, released 2025-02-10)[^1]


### **Gemini 1.5 Models**

**Gemini 1.5 Pro**

- `gemini-1.5-pro-002` - Latest stable version (Upcoming Retirement on 2025-09-24)[^1]
- `gemini-1.5-pro-001` - Initial stable version (Upcoming Retirement on 2025-05-24)[^1]
- `gemini-1.5-pro` - Auto-updated alias pointing to latest stable version[^1]

**Gemini 1.5 Flash**

- `gemini-1.5-flash-002` - Latest stable version (Upcoming Retirement on 2025-09-24)[^1]
- `gemini-1.5-flash-001` - Initial stable version (Upcoming Retirement on 2025-05-24)[^1]
- `gemini-1.5-flash` - Auto-updated alias pointing to latest stable version[^1]


### **Legacy Models (Retired)**

**Gemini 1.0 Pro Vision**

- `gemini-1.0-pro-vision-001` - Retired as of 2025-04-21[^1]
- `gemini-1.0-pro-vision` - Retired as of 2025-04-21[^1]

**Gemini 1.0 Pro**

- `gemini-1.0-pro-002` - Retired as of 2025-04-21[^1]
- `gemini-1.0-pro-001` - Retired as of 2025-04-21[^1]
- `gemini-1.0-pro` - Retired as of 2025-04-21[^1]


## Rate Limits by Tier

### **Free Tier Limits**

| Model | RPM | TPM | RPD |
| :-- | :-- | :-- | :-- |
| Gemini 2.5 Flash Preview 05-20 | 10 | 250,000 | 500 |
| Gemini 2.5 Pro Preview 03-25 | 5 | 250,000 | 25 |
| Gemini 2.0 Flash | 15 | 1,000,000 | 1,500 |
| Gemini 2.0 Flash-Lite | 30 | 1,000,000 | 1,500 |
| Gemini 1.5 Flash | 15 | 250,000 | 500 |
| Gemini 1.5 Flash-8B | 15 | 250,000 | 500 |



## Rate Limit Dimensions

Rate limits are measured across four dimensions[^2]:

- **RPM**: Requests per minute
- **RPD**: Requests per day
- **TPM**: Tokens per minute
- **TPD**: Tokens per day

Usage is evaluated against each limit, and exceeding any of them will trigger a rate limit error[^2]. Rate limits are applied at the project level, not per API key[^2].

## Important Notes

**Preview and Experimental Models** have more restrictive rate limits compared to stable versions[^2].

**Firebase AI Logic** adds an additional "per user" rate limit of 100 RPM when using Firebase AI Logic SDKs[^4].

For the most current model availability, you can programmatically list all available models using the REST API endpoints: `models.list` for Gemini Developer API or `publishers.models.list` for Vertex AI Gemini API[^1].

<div style="text-align: center">⁂</div>

[^1]: https://firebase.google.com/docs/vertex-ai/gemini-models

[^2]: https://ai.google.dev/gemini-api/docs/rate-limits

[^3]: https://www.googlecloudcommunity.com/gc/Gemini-Code-Assist/You-ve-reached-your-rate-limit-Please-try-again-later-Gemini-1-5/m-p/739827

[^4]: https://firebase.google.com/docs/ai-logic/quotas

[^5]: https://www.googlecloudcommunity.com/gc/AI-ML/Limited-request-per-minute-5-RPM-for-Gemini-models/m-p/752113

[^6]: https://ai.google.dev/gemini-api/docs/models

[^7]: https://cloud.google.com/vertex-ai/generative-ai/docs/models

[^8]: https://deepmind.google/models/gemini/

[^9]: https://ai.google.dev/gemini-api/docs/pricing

[^10]: https://support.google.com/gemini/thread/338651274/need-to-know-the-rate-limits-for-gemini-2-5-pro-experimental

[^11]: https://www.reddit.com/r/SillyTavernAI/comments/1jltjij/gemini_25_without_rpm_or_daily_use_limit_help/

[^12]: https://www.reddit.com/r/Bard/comments/1jzsezp/gemini_25_pro_experimental_usage_limits/

[^13]: https://ai.google.dev/api/models

[^14]: https://gist.github.com/DF-wu/72ec3a7c2ff3247fc33b3eda07e048d0

[^15]: https://developer.box.com/guides/box-ai/ai-models/google-gemini-1-5-pro-001-model-card/

[^16]: https://firebase.google.com/docs/ai-logic/models

[^17]: https://docs.litellm.ai/docs/providers/gemini

[^18]: https://www.kolena.com/guides/working-with-gemini-api-quick-start-for-developers/

[^19]: https://developers.googleblog.com/en/updated-gemini-models-reduced-15-pro-pricing-increased-rate-limits-and-more/

[^20]: https://www.youtube.com/watch?v=eWTV4gs57Do

